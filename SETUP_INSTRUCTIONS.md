# RobotWars - Setup Instructions

## Current Status ✅

We've successfully created the basic project structure with:

- **Project Configuration**: Godot 4 project with pixel-perfect settings
- **Input System**: 8-directional movement (WASD/Arrow keys) + Fire (Space)
- **GameManager**: Singleton for game state management
- **Room Generator**: Procedural maze generation system
- **Player Controller**: 8-directional movement with wall collision detection
- **Main Scene**: Basic game loop with HUD

## What's Working

### 🏗️ Project Structure
```
RobotWars/
├── project.godot          # Main project configuration
├── scenes/
│   └── Main.tscn         # Main game scene with HUD
├── scripts/
│   ├── GameManager.gd    # Game state singleton
│   ├── RoomGenerator.gd  # Maze generation
│   ├── Player.gd         # Player controller
│   └── Main.gd           # Main scene controller
└── assets/               # Ready for sprites/sounds
```

### 🎮 Core Systems
- **Maze Generation**: Creates random room layouts with walls and exits
- **Player Movement**: Smooth 8-directional movement at 120 pixels/second
- **Wall Collision**: Instant death when touching cyan walls (<PERSON><PERSON><PERSON>k style)
- **Camera System**: Follows player with pixel-perfect rendering
- **HUD**: Score, Lives, and Room counter display

## Next Steps to Run

### 1. Install Godot 4
Download Godot 4.x from [godotengine.org](https://godotengine.org/download)

### 2. Open Project
1. Launch Godot
2. Click "Import" 
3. Navigate to this folder and select `project.godot`
4. Click "Import & Edit"

### 3. Test the Game
1. Press F5 or click the Play button
2. Select `scenes/Main.tscn` as the main scene when prompted
3. Use WASD or Arrow Keys to move the green square (player)
4. Try touching the cyan walls - you should die instantly!

## Current Gameplay

- **Green Square**: Your player character
- **Cyan Walls**: Instant death on contact
- **Black Background**: Safe walking area
- **Movement**: 8-directional with WASD/Arrow keys
- **Death**: White flash effect, respawn at start position

## What We're Building Next

1. **Shooting System**: Laser projectiles in movement direction
2. **Robot Enemies**: Colored stick-figure robots with AI
3. **Evil Otto**: The bouncing smiley face of doom
4. **Proper Sprites**: Replace colored rectangles with pixel art

## Technical Notes

### Pixel-Perfect Settings
- Resolution: 800x600 (4:3 aspect ratio)
- No texture filtering (sharp pixels)
- Viewport stretch mode for consistent scaling

### Physics Layers
- Layer 1: Walls (collision only)
- Layer 2: Player 
- Layer 3: Enemies (coming next)
- Layer 4: Projectiles (coming next)
- Layer 5: Evil Otto (coming later)

### Performance
- Target: 60 FPS
- Grid-based collision for efficiency
- Object pooling ready for projectiles

## Troubleshooting

### If the game doesn't start:
1. Make sure you selected `scenes/Main.tscn` as the main scene
2. Check the Output panel for error messages
3. Verify all script files are properly attached

### If movement feels wrong:
- Movement speed is set to 120 pixels/second
- Acceleration/friction values can be adjusted in Player.gd

### If walls don't kill you:
- Check that wall collision detection is working
- Walls should be in the "walls" group with collision layer 1

## Ready to Continue?

Once you've tested the basic movement and maze generation, we can proceed to:
1. Add the shooting system
2. Create robot enemies
3. Implement the AI behavior from our example

The foundation is solid - let's build the fun parts! 🤖⚡
