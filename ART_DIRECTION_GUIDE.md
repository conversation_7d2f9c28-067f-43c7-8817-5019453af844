# RobotWars - Art Direction Guide

## Visual Philosophy
RobotWars embraces authentic 16-bit aesthetics with clean pixel art, limited color palettes, and sharp, unfiltered graphics that evoke the golden age of arcade gaming.

## Technical Specifications

### Resolution & Display
- **Game Resolution**: 800x600 pixels (4:3 aspect ratio)
- **Pixel Density**: 1:1 pixel mapping (no scaling artifacts)
- **Color Depth**: 16 colors maximum per sprite
- **Background**: Pure black (#000000) for maximum contrast

### Sprite Dimensions
- **Player Character**: 16x16 pixels
- **Robot Enemies**: 16x16 pixels
- **Evil Otto**: 24x24 pixels (larger for intimidation)
- **Wall Tiles**: 16x16 pixels
- **Projectiles**: 4x4 pixels
- **Explosion Effects**: 32x32 pixels (2x2 tile area)

## Color Palette

### Primary Palette (Core Game)
```
Black:      #000000  (Background, outlines)
White:      #FFFFFF  (Projectiles, highlights)
Cyan:       #00FFFF  (Walls, UI elements)
Green:      #00FF00  (Player character)
Red:        #FF0000  (Enemy robots, danger)
Blue:       #0000FF  (Enemy robots, cool)
Yellow:     #FFFF00  (Enemy robots, Evil Otto)
Purple:     #FF00FF  (Enemy robots, rare)
Orange:     #FF8000  (Explosions, warnings)
Gray:       #808080  (Shadows, disabled elements)
```

### Secondary Palette (Effects & UI)
```
Dark Red:   #800000  (Explosion cores)
Dark Blue:  #000080  (Deep shadows)
Light Gray: #C0C0C0  (Text, UI borders)
Dark Gray:  #404040  (Subtle details)
Lime:       #80FF00  (Power-ups, special effects)
Pink:       #FF8080  (Damage indicators)
```

## Sprite Design Guidelines

### Character Design Principles
1. **Stick Figure Aesthetic**: Simple geometric shapes
2. **High Contrast**: Bold colors against black background
3. **Readable Silhouettes**: Clear shapes at small sizes
4. **Minimal Detail**: Focus on essential features only
5. **Consistent Proportions**: All humanoids same basic size

### Player Character Specifications
```
Sprite: 16x16 pixels
Colors: Green (#00FF00), Black (#000000)
Design: 
- Head: 4x4 pixel square
- Body: 2x6 pixel rectangle
- Arms: 2x4 pixel rectangles
- Legs: 2x4 pixel rectangles
Animation Frames: 4 (idle, walk1, walk2, walk3)
```

### Robot Enemy Specifications
```
Sprite: 16x16 pixels
Colors: Primary robot color + Black outlines
Design:
- Head: 4x4 pixel square (angular/robotic)
- Body: 3x6 pixel rectangle
- Arms: 2x4 pixel rectangles (mechanical joints)
- Legs: 2x4 pixel rectangles (boxy feet)
Animation Frames: 3 (idle, walk1, walk2)
Variants: Red, Blue, Yellow, Purple robots
```

### Evil Otto Specifications
```
Sprite: 24x24 pixels
Colors: Yellow (#FFFF00), Black (#000000)
Design:
- Circle: 20x20 pixel filled circle
- Eyes: Two 3x3 pixel black circles
- Mouth: 8x3 pixel black arc (smiling)
Animation Frames: 2 (bounce up, bounce down)
Special: Slight squash/stretch on bounce
```

## Environment Art

### Wall Tile System
```
Tile Size: 16x16 pixels
Color: Cyan (#00FFFF)
Style: Solid blocks with subtle inner border
Variants:
- Solid wall
- Corner pieces (4 directions)
- T-junctions (4 orientations)
- Cross intersections
```

### Room Layout Principles
- **Grid-based**: Everything aligns to 16px grid
- **Clear Pathways**: Minimum 32px wide corridors
- **Visual Hierarchy**: Walls bright, background dark
- **Exit Indicators**: Subtle visual cues for room exits

## Animation Guidelines

### Frame Timing
- **Player Walk Cycle**: 8 frames per second
- **Robot Movement**: 6 frames per second
- **Evil Otto Bounce**: 4 frames per second
- **Explosions**: 12 frames per second (fast)
- **Projectiles**: No animation (single frame)

### Animation Principles
1. **Minimal Frames**: 2-4 frames maximum per animation
2. **Sharp Transitions**: No easing or smoothing
3. **Consistent Timing**: Same frame rate for similar actions
4. **Readable Motion**: Clear pose-to-pose changes

## UI Design

### HUD Elements
```
Score Display:
- Font: Monospace pixel font
- Size: 16px height
- Color: White (#FFFFFF)
- Position: Top-left corner

Lives Display:
- Mini player sprites (8x8 pixels)
- Color: Green (#00FF00)
- Position: Top-right corner

Room Counter:
- Format: "ROOM XX"
- Font: Same as score
- Position: Top-center
```

### Menu Design
- **Background**: Black with cyan borders
- **Text**: White pixel font
- **Highlights**: Yellow for selected items
- **Layout**: Centered, minimal decoration

## Sprite Sheet Organization

### Player Sprite Sheet (64x16 pixels)
```
Frame 1: Idle (0,0 to 15,15)
Frame 2: Walk 1 (16,0 to 31,15)
Frame 3: Walk 2 (32,0 to 47,15)
Frame 4: Walk 3 (48,0 to 63,15)
```

### Robot Sprite Sheet (48x64 pixels)
```
Red Robot:    (0,0 to 47,15)   - 3 frames
Blue Robot:   (0,16 to 47,31)  - 3 frames
Yellow Robot: (0,32 to 47,47)  - 3 frames
Purple Robot: (0,48 to 47,63)  - 3 frames
```

### Effects Sprite Sheet (128x32 pixels)
```
Explosion 1: (0,0 to 31,31)
Explosion 2: (32,0 to 63,31)
Explosion 3: (64,0 to 95,31)
Explosion 4: (96,0 to 127,31)
```

## Visual Effects

### Projectile Trails
- **Length**: 3 pixels behind projectile
- **Fade**: Gradual alpha reduction
- **Color**: Same as projectile (white)

### Explosion Animation
- **Duration**: 333ms (4 frames at 12fps)
- **Colors**: Orange → Red → Dark Red → Fade
- **Size**: Grows from 16x16 to 32x32 pixels
- **Effect**: Screen shake (2 pixels, 100ms)

### Death Effects
- **Player Death**: White flash, explosion animation
- **Robot Death**: Colored flash matching robot, explosion
- **Wall Contact**: Instant white flash, no explosion

## Asset Creation Workflow

### Tools Recommended
- **Aseprite**: Professional pixel art editor
- **GIMP**: Free alternative with pixel art plugins
- **Photoshop**: With nearest-neighbor scaling
- **GraphicsGale**: Specialized for game sprites

### Export Settings
- **Format**: PNG with transparency
- **Filtering**: None (nearest-neighbor only)
- **Compression**: Lossless
- **Color Profile**: sRGB
- **Metadata**: Remove all EXIF data

### Quality Checklist
- [ ] No anti-aliasing or smoothing
- [ ] Colors match exact hex values
- [ ] Sprites align to pixel grid
- [ ] Transparent backgrounds where needed
- [ ] Consistent lighting direction
- [ ] Readable at target resolution
