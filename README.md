# RobotWars - 16-Bit Berzerk-Inspired Game

A retro arcade game that captures the fast-paced, emergent gameplay of the classic Berzerk (1980) with authentic 16-bit pixel art aesthetics.

## 🎮 Game Overview

RobotWars is a maze-based action game where players navigate dangerous robot-filled rooms as a green stick-figure humanoid. The core gameplay revolves around:

- **8-directional movement** through maze-like rooms
- **Single-button laser combat** against colored robot enemies
- **Emergent AI behavior** where robots make mistakes and shoot each other
- **Time pressure** from <PERSON>, an indestructible bouncing smiley face
- **Instant death** from walls, enemy fire, or Evil Otto contact

## 📁 Project Structure

```
RobotWars/
├── GAME_DESIGN_DOCUMENT.md    # Complete gameplay specifications
├── TECHNICAL_IMPLEMENTATION.md # Engine choice and architecture
├── ART_DIRECTION_GUIDE.md     # Visual style and asset specifications
├── robot_ai_example.gd        # Example Robot AI implementation
└── README.md                  # This file
```

## 🎯 Core Features

### Gameplay Mechanics
- **Player**: Green stick-figure with 8-directional movement and laser weapon
- **Enemies**: Colored robot stick-figures with flawed AI behavior
- **Evil Otto**: Unstoppable bouncing smiley face that appears after 30 seconds
- **Environment**: Cyan-walled mazes with instant-death wall contact
- **Scoring**: Points for robot destruction regardless of cause

### Emergent Behaviors
- Robots accidentally shoot each other (15% chance)
- Robots run into walls and self-destruct (10% chance)
- Chain reactions from explosions
- Panic behavior when Evil Otto appears

## 🛠 Technical Implementation

### Recommended Engine: Godot 4
- Perfect for pixel-perfect 2D rendering
- Lightweight and fast iteration
- Built-in physics and scene system
- Cross-platform deployment
- Free and open-source

### Key Systems
- **State-based AI** for robot behavior
- **Grid-based room generation** for maze layouts
- **Object pooling** for performance optimization
- **Pixel-perfect rendering** pipeline

## 🎨 Visual Style

### 16-Bit Aesthetic
- **Resolution**: 800x600 (4:3 aspect ratio)
- **Color Palette**: Limited 16-color palette per sprite
- **Pixel Art**: Sharp, unfiltered graphics
- **Animation**: 2-4 frames per character

### Color Scheme
- **Background**: Pure black (#000000)
- **Walls**: Bright cyan (#00FFFF)
- **Player**: Bright green (#00FF00)
- **Robots**: Red, Blue, Yellow, Purple
- **Evil Otto**: Yellow with black features

## 🤖 AI Implementation Example

The included `robot_ai_example.gd` demonstrates:

### AI States
- **PATROL**: Random movement with direction changes
- **HUNT**: Move toward detected player
- **SHOOT**: Fire laser when player in line of sight
- **PANIC**: Erratic behavior when Evil Otto present
- **STUNNED**: Brief pause after mistakes

### Emergent Behaviors
```gdscript
# Mistake chances for authentic Berzerk feel
var wall_collision_chance: float = 0.10    # Run into walls
var friendly_fire_chance: float = 0.15     # Shoot wrong target
var panic_mistake_chance: float = 0.25     # Erratic panic behavior
```

## 🚀 Getting Started

### Development Setup
1. **Install Godot 4**: Download from [godotengine.org](https://godotengine.org)
2. **Create New Project**: Use the provided project structure
3. **Import Assets**: Follow art direction guide for sprite creation
4. **Implement Core Systems**: Start with player movement and basic AI

### Asset Creation
- **Sprites**: 16x16 pixels for characters, 24x24 for Evil Otto
- **Tools**: Aseprite, GIMP, or Photoshop with pixel art settings
- **Export**: PNG format, no filtering, exact color matching

### Performance Targets
- **60 FPS** at 800x600 resolution
- **Sub-frame input response** for player controls
- **Maximum 50 entities** on screen simultaneously

## 🎵 Audio Design

### Sound Effects
- **8-bit style** laser shots and explosions
- **Retro speech synthesis** for robot taunts
- **Spatial audio** for immersive experience

### Classic Phrases
- "Chicken! Fight like a robot!"
- "The humanoid must not escape!"
- "Intruder alert!"

## 📊 Progression System

### Difficulty Scaling
- **More robots** per room (+1 every 3 rooms)
- **Faster movement** (+10% every 5 rooms)
- **Earlier Evil Otto** (-2 seconds every 10 rooms)
- **Color palette shifts** every 10 rooms

### Scoring
- Robot destroyed by player: **50 points**
- Robot self-destructs: **25 points**
- Robot destroyed by other robot: **25 points**
- Room clear bonus: **100 points**

## 🎮 Controls

- **Movement**: WASD or Arrow Keys (8-directional)
- **Fire**: Spacebar (shoots in movement direction)
- **Pause**: Escape key

## 📋 Development Roadmap

### Phase 1: Core Systems
- [ ] Player movement and shooting
- [ ] Basic robot AI implementation
- [ ] Wall collision detection
- [ ] Room generation system

### Phase 2: Game Features
- [ ] Evil Otto implementation
- [ ] Scoring system
- [ ] Room transitions
- [ ] Audio integration

### Phase 3: Polish
- [ ] Visual effects and animations
- [ ] Menu system and UI
- [ ] Save/load functionality
- [ ] Performance optimization

## 🎯 Design Philosophy

RobotWars stays true to the elegant simplicity of Berzerk while enhancing it with smooth 16-bit presentation. The focus is on:

- **Emergent gameplay** through AI imperfection
- **Time pressure** via Evil Otto mechanics
- **Simple controls** with deep strategic possibilities
- **Authentic retro aesthetics** without modern complexity

## 📝 Notes

This design document provides a complete foundation for building RobotWars. The included example code demonstrates the core AI system that creates the emergent, chaotic gameplay that made Berzerk memorable.

The key to success is maintaining the balance between player skill and AI unpredictability that creates moments of tension, relief, and unexpected outcomes - the hallmark of great arcade games.

---

*Ready to build the next classic arcade experience? Start with the Game Design Document and work through each system methodically. Remember: simplicity and tight controls are more important than complex features.*
