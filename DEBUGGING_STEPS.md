# RobotWars - Debugging Steps

## Issues Fixed ✅

1. **Signal naming conflict**: Changed `game_over()` function to `trigger_game_over()`
2. **Missing player instantiation**: Fixed Main.gd to properly create player with ColorRect
3. **AnimatedSprite2D references**: Replaced with ColorRect for simple testing
4. **HUD connections**: Added proper signal connections for score/lives/room display

## Current Status

The project should now:
- Show a **green square** (player) that you can move with WASD/Arrow keys
- Display **cyan walls** around the room that kill you on contact
- Show **HUD** with Score: 0, Lives: 3, Room 1
- Print debug messages when you press movement keys

## Testing Steps

### 1. Import and Run
1. Open Godot 4
2. Import the project (select `project.godot`)
3. Press F5 to run
4. Select `scenes/Main.tscn` as main scene

### 2. Expected Behavior
- **Green square**: Your player character (16x16 pixels)
- **Cyan walls**: Around the room edges and some internal walls
- **Black background**: Safe walking area
- **Movement**: WASD or Arrow keys should move the green square
- **Wall death**: Touching cyan walls should kill you (white flash, respawn)

### 3. Debug Output
Check the Output panel in Godot for:
- "Room generated with exits at: [positions]"
- "Player spawned at: [position]"
- "Input detected: [direction]" when you press keys

## If Still Not Working

### Check Input Map
1. Go to Project → Project Settings → Input Map
2. Verify these actions exist:
   - `move_up` (W, Up Arrow)
   - `move_down` (S, Down Arrow)
   - `move_left` (A, Left Arrow)
   - `move_right` (D, Right Arrow)

### Check Scene Structure
In the Remote Inspector while running:
```
Main (Node2D)
├── Camera2D
├── RoomGenerator (Node2D)
│   └── [Multiple StaticBody2D nodes with ColorRect children]
├── Player (CharacterBody2D)
│   ├── ColorRect
│   └── CollisionShape2D
└── CanvasLayer
    └── HUD
        ├── ScoreLabel
        ├── LivesLabel
        └── RoomLabel
```

### Common Issues

1. **No movement**: 
   - Check if Player node exists in scene tree
   - Verify input actions are properly mapped
   - Look for error messages in Output

2. **No walls visible**:
   - Check if RoomGenerator created StaticBody2D nodes
   - Verify ColorRect children are visible

3. **Crashes on startup**:
   - Check for script errors in Output panel
   - Verify all @onready references are valid

## Quick Fix Test

If the above doesn't work, try this minimal test:

1. Create a new scene
2. Add a CharacterBody2D as root
3. Add a ColorRect child (16x16, green color)
4. Add a CollisionShape2D child with RectangleShape2D
5. Attach this simple script:

```gdscript
extends CharacterBody2D

func _physics_process(delta):
    var input = Vector2.ZERO
    if Input.is_action_pressed("ui_up"): input.y -= 1
    if Input.is_action_pressed("ui_down"): input.y += 1
    if Input.is_action_pressed("ui_left"): input.x -= 1
    if Input.is_action_pressed("ui_right"): input.x += 1
    
    velocity = input.normalized() * 200
    move_and_slide()
```

This should give you a movable green square to test basic functionality.

## Next Steps

Once movement is working:
1. Test wall collision (should kill player)
2. Verify HUD updates
3. Check room generation
4. Move on to shooting system

Let me know what you see and any error messages! 🐛🔧
