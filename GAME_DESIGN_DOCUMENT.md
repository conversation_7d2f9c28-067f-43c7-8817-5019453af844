# RobotWars - Game Design Document

## Overview
RobotWars is a 16-bit inspired arcade game based on the classic Berzerk (1980) mechanics, featuring fast-paced maze navigation, robot combat, and emergent AI behavior.

## Core Gameplay Loop
1. Player spawns in a maze-like room filled with robot enemies
2. Navigate using 8-directional movement while avoiding walls (instant death)
3. Shoot robots with laser weapon (single fire button)
4. Clear room by destroying all robots OR escape through exits
5. Progress to next procedurally generated room
6. <PERSON> Otto appears if player takes too long, forcing urgency

## Controls
- **Movement**: 8-directional (WASD or Arrow Keys + diagonals)
- **Fire**: Single button (Spacebar) - shoots in direction of movement
- **Pause**: ESC key

## Player Character
- **Appearance**: Green stick-figure humanoid (16x16 pixels)
- **Movement**: Smooth 8-directional at 120 pixels/second
- **Weapon**: Laser that travels at 200 pixels/second
- **Health**: Single hit point (one-shot death)

## Enemies

### Robot Enemies
- **Appearance**: Colored stick-figure robots (Red, Blue, Yellow, Purple)
- **Size**: 16x16 pixels
- **Behavior**: 
  - Move randomly through maze
  - Shoot at player when in line of sight
  - Can accidentally shoot each other
  - Can run into walls and self-destruct
  - Basic pathfinding with intentional flaws for emergent behavior

### Evil Otto
- **Appearance**: Bouncing yellow smiley face (24x24 pixels)
- **Spawn Condition**: Appears after 30 seconds in same room
- **Behavior**: 
  - Bounces through walls
  - Moves directly toward player
  - Destroys everything in path (including robots)
  - Cannot be destroyed
  - Speed increases over time

## Environment

### Maze Structure
- **Room Size**: 800x600 pixels (50x37.5 tiles at 16px each)
- **Wall Thickness**: 16 pixels
- **Wall Color**: Bright cyan (#00FFFF)
- **Background**: Black (#000000)
- **Exits**: 1-4 per room, positioned on room edges

### Death Conditions
1. Shot by robot laser
2. Touching any wall
3. Caught by Evil Otto
4. Robot explosion proximity (8 pixel radius)

## Scoring System
- **Robot Destroyed by Player**: 50 points
- **Robot Self-Destructs**: 25 points
- **Robot Destroyed by Other Robot**: 25 points
- **Robot Destroyed by Evil Otto**: 10 points
- **Room Clear Bonus**: 100 points
- **Escape Bonus**: 50 points

## AI Behavior

### Robot AI States
1. **Patrol**: Random movement with direction changes every 1-3 seconds
2. **Hunt**: Move toward player when detected (line of sight)
3. **Shoot**: Fire laser when player is in line of sight
4. **Panic**: Erratic movement when Evil Otto is present

### Emergent Behaviors
- 15% chance robots shoot at wrong target
- 10% chance robots move into walls
- Robots can block each other's shots
- Chain reactions from explosions

## Progression System
- **Room Generation**: Procedural maze layouts with guaranteed solutions
- **Difficulty Scaling**: 
  - More robots per room (+1 every 3 rooms)
  - Faster robot movement (+10% every 5 rooms)
  - Evil Otto appears sooner (-2 seconds every 10 rooms)
- **Room Themes**: Color palette shifts every 10 rooms

## Audio Design
- **Sound Effects**: 8-bit style laser shots, explosions, footsteps
- **Speech Synthesis**: Retro robot voice phrases:
  - "Chicken! Fight like a robot!"
  - "The humanoid must not escape!"
  - "Intruder alert!"
- **Music**: Minimal ambient electronic soundtrack

## Visual Style

### 16-Bit Aesthetic
- **Resolution**: 800x600 (4:3 aspect ratio)
- **Color Palette**: Limited to 16 colors per sprite
- **Pixel Art**: Sharp, clean pixels with no anti-aliasing
- **Animation**: 2-4 frames per character animation

### Color Palette
- **Player**: Bright green (#00FF00)
- **Walls**: Cyan (#00FFFF)
- **Background**: Black (#000000)
- **Robots**: Red (#FF0000), Blue (#0000FF), Yellow (#FFFF00), Purple (#FF00FF)
- **Evil Otto**: Yellow (#FFFF00) with black features
- **Lasers**: White (#FFFFFF)
- **Explosions**: Orange/Red gradient

## Technical Requirements
- **Frame Rate**: 60 FPS
- **Input Lag**: <16ms
- **Room Transitions**: <1 second loading
- **Save System**: High scores and current room progress
