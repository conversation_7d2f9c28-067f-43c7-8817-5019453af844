list=[{
"base": &"CharacterBody2D",
"class": &"BerzerkRobot",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/BerzerkRobot.gd"
}, {
"base": &"Area2D",
"class": &"Laser",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Laser.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Player",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Player.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Robot",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://robot_ai_example.gd"
}, {
"base": &"Node2D",
"class": &"RoomGenerator",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/RoomGenerator.gd"
}]
