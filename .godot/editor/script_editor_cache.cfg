[res://scripts/GameManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 5,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 94,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/RoomGenerator.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 28,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 197,
"scroll_position": 141.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Main.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 73,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 8,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
