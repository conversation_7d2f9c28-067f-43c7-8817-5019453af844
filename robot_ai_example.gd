# RobotWars - Robot AI Example Implementation (Godot GDScript)
# This script demonstrates the core AI behavior system for enemy robots

extends CharacterBody2D
class_name Robot

# AI States
enum AIState {
	PATROL,
	HUNT,
	SHOOT,
	PANIC,
	STUNNED
}

# Robot properties
@export var robot_color: String = "red"
@export var move_speed: float = 80.0
@export var shoot_cooldown: float = 1.5
@export var detection_range: float = 200.0
@export var panic_speed_multiplier: float = 1.5

# Internal state
var current_state: AIState = AIState.PATROL
var player_reference: Node2D
var last_player_position: Vector2
var state_timer: float = 0.0
var shoot_timer: float = 0.0
var movement_direction: Vector2 = Vector2.RIGHT
var direction_change_timer: float = 0.0
var is_alive: bool = true

# Emergent behavior chances (for mistakes)
var wall_collision_chance: float = 0.10  # 10% chance to run into walls
var friendly_fire_chance: float = 0.15   # 15% chance to shoot wrong target
var panic_mistake_chance: float = 0.25   # 25% chance for erratic behavior when <PERSON> present

# References
@onready var sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var laser_spawn_point: Marker2D = $LaserSpawnPoint
@onready var detection_area: Area2D = $DetectionArea
@onready var wall_detector: RayCast2D = $WallDetector

# Preloaded scenes
var laser_scene = preload("res://scenes/Laser.tscn")

func _ready():
	# Initialize robot with random direction
	movement_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
	direction_change_timer = randf_range(1.0, 3.0)
	
	# Set up detection area
	detection_area.body_entered.connect(_on_player_detected)
	detection_area.body_exited.connect(_on_player_lost)
	
	# Set robot color
	sprite.modulate = get_robot_color(robot_color)

func _physics_process(delta):
	if not is_alive:
		return
		
	# Update timers
	state_timer += delta
	shoot_timer += delta
	direction_change_timer -= delta
	
	# Check for Evil Otto presence (panic trigger)
	check_for_evil_otto()
	
	# Execute current AI state
	match current_state:
		AIState.PATROL:
			execute_patrol_behavior(delta)
		AIState.HUNT:
			execute_hunt_behavior(delta)
		AIState.SHOOT:
			execute_shoot_behavior(delta)
		AIState.PANIC:
			execute_panic_behavior(delta)
		AIState.STUNNED:
			execute_stunned_behavior(delta)
	
	# Apply movement with collision detection
	move_and_slide()
	
	# Check for wall collision (potential self-destruction)
	check_wall_collision()

func execute_patrol_behavior(delta):
	# Random patrol movement with direction changes
	if direction_change_timer <= 0.0:
		# Chance for AI mistake - random direction including walls
		if randf() < wall_collision_chance:
			movement_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
		else:
			# Smart direction change - avoid walls
			movement_direction = get_safe_direction()
		
		direction_change_timer = randf_range(1.0, 3.0)
	
	# Move in current direction
	velocity = movement_direction * move_speed
	
	# Update sprite direction
	update_sprite_direction()

func execute_hunt_behavior(delta):
	if player_reference == null:
		change_state(AIState.PATROL)
		return
	
	# Calculate direction to player
	var direction_to_player = (player_reference.global_position - global_position).normalized()
	
	# Check if we have line of sight for shooting
	if has_line_of_sight_to_player():
		change_state(AIState.SHOOT)
		return
	
	# Move toward player with some AI imperfection
	if randf() < 0.1:  # 10% chance for movement mistake
		movement_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
	else:
		movement_direction = direction_to_player
	
	velocity = movement_direction * move_speed
	update_sprite_direction()

func execute_shoot_behavior(delta):
	if player_reference == null:
		change_state(AIState.PATROL)
		return
	
	# Stop moving while shooting
	velocity = Vector2.ZERO
	
	# Check if we can still shoot at player
	if not has_line_of_sight_to_player():
		change_state(AIState.HUNT)
		return
	
	# Shoot at player (with chance for friendly fire)
	if shoot_timer >= shoot_cooldown:
		fire_laser()
		shoot_timer = 0.0
		
		# After shooting, brief pause then return to hunting
		state_timer = 0.0
		change_state(AIState.HUNT)

func execute_panic_behavior(delta):
	# Erratic movement when Evil Otto is present
	if direction_change_timer <= 0.0:
		# High chance for mistakes during panic
		if randf() < panic_mistake_chance:
			movement_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
		else:
			# Try to move away from Evil Otto
			var otto = get_tree().get_first_node_in_group("evil_otto")
			if otto:
				movement_direction = (global_position - otto.global_position).normalized()
			else:
				movement_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
		
		direction_change_timer = randf_range(0.2, 0.8)  # Faster direction changes
	
	velocity = movement_direction * move_speed * panic_speed_multiplier
	update_sprite_direction()

func execute_stunned_behavior(delta):
	# Brief pause after being hit or making mistake
	velocity = Vector2.ZERO
	
	if state_timer >= 0.5:  # Stunned for 0.5 seconds
		change_state(AIState.PATROL)

func fire_laser():
	var laser = laser_scene.instantiate()
	get_parent().add_child(laser)
	laser.global_position = laser_spawn_point.global_position
	
	# Determine target with chance for friendly fire
	var target_direction: Vector2
	
	if randf() < friendly_fire_chance:
		# Shoot at random robot or random direction
		var robots = get_tree().get_nodes_in_group("robots")
		if robots.size() > 1:
			var random_robot = robots[randi() % robots.size()]
			if random_robot != self:
				target_direction = (random_robot.global_position - global_position).normalized()
			else:
				target_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
		else:
			target_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
	else:
		# Shoot at player
		if player_reference:
			target_direction = (player_reference.global_position - global_position).normalized()
		else:
			target_direction = movement_direction
	
	laser.set_direction(target_direction)
	laser.set_owner_type("robot")

func has_line_of_sight_to_player() -> bool:
	if player_reference == null:
		return false
	
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsRayQueryParameters2D.create(
		global_position,
		player_reference.global_position
	)
	query.exclude = [self]
	query.collision_mask = 1  # Only check walls
	
	var result = space_state.intersect_ray(query)
	return result.is_empty()  # True if no walls between robot and player

func get_safe_direction() -> Vector2:
	# Try to find direction that doesn't lead to immediate wall collision
	var directions = [
		Vector2.UP, Vector2.DOWN, Vector2.LEFT, Vector2.RIGHT,
		Vector2.UP + Vector2.LEFT, Vector2.UP + Vector2.RIGHT,
		Vector2.DOWN + Vector2.LEFT, Vector2.DOWN + Vector2.RIGHT
	]
	
	for direction in directions:
		wall_detector.target_position = direction * 32  # Check 32 pixels ahead
		wall_detector.force_raycast_update()
		
		if not wall_detector.is_colliding():
			return direction.normalized()
	
	# If all directions blocked, return random (AI will make mistake)
	return Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()

func check_wall_collision():
	# Check if robot ran into wall (self-destruct)
	if get_slide_collision_count() > 0:
		var collision = get_slide_collision(0)
		if collision.get_collider().is_in_group("walls"):
			self_destruct()

func check_for_evil_otto():
	var otto = get_tree().get_first_node_in_group("evil_otto")
	if otto and current_state != AIState.PANIC:
		change_state(AIState.PANIC)
	elif not otto and current_state == AIState.PANIC:
		change_state(AIState.PATROL)

func change_state(new_state: AIState):
	current_state = new_state
	state_timer = 0.0

func update_sprite_direction():
	# Simple 4-direction sprite facing
	if abs(movement_direction.x) > abs(movement_direction.y):
		sprite.flip_h = movement_direction.x < 0
	
	# Play walking animation if moving
	if velocity.length() > 0:
		sprite.play("walk")
	else:
		sprite.play("idle")

func get_robot_color(color_name: String) -> Color:
	match color_name:
		"red": return Color.RED
		"blue": return Color.BLUE
		"yellow": return Color.YELLOW
		"purple": return Color.MAGENTA
		_: return Color.RED

func self_destruct():
	# Create explosion effect
	var explosion = preload("res://scenes/Explosion.tscn").instantiate()
	get_parent().add_child(explosion)
	explosion.global_position = global_position
	
	# Award points to game manager
	GameManager.add_score(25)  # Self-destruct points
	
	# Remove robot
	is_alive = false
	queue_free()

func take_damage():
	self_destruct()

func _on_player_detected(body):
	if body.is_in_group("player"):
		player_reference = body
		if current_state == AIState.PATROL:
			change_state(AIState.HUNT)

func _on_player_lost(body):
	if body.is_in_group("player"):
		last_player_position = body.global_position
		# Don't immediately lose player, keep hunting for a bit
