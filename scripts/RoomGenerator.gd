# RoomGenerator.gd - Procedural maze generation for RobotWars
extends Node2D
class_name RoomGenerator

# Room dimensions - authentic Berzerk uses 3x5 grid of cells
const ROOM_WIDTH = 50   # 800 pixels / 16 = 50 tiles
const ROOM_HEIGHT = 37  # 600 pixels / 16 = 37.5, rounded down
const TILE_SIZE = 16

# Berzerk grid system - 3x5 cells, each ~160x120 pixels (10x7.5 tiles)
const GRID_COLS = 5
const GRID_ROWS = 3
const CELL_WIDTH = 10   # tiles per cell
const CELL_HEIGHT = 12  # tiles per cell

# We create wall tiles programmatically

# Room data
var room_grid: Array = []
var exits: Array = []

# Room generation settings
@export var min_path_width: int = 2
@export var max_wall_density: float = 0.4
@export var guaranteed_exits: int = 2

func _ready():
	print("RoomGenerator: Starting room generation...")
	generate_room()
	print("RoomGenerator: Room generation complete!")

func generate_room():
	# Initialize empty grid
	initialize_grid()

	# Generate simple Berzerk-style room layout
	generate_simple_room()

	# Create exits
	create_exits()

	# Build the visual room
	build_room_visuals()

func initialize_grid():
	room_grid.clear()
	for y in range(ROOM_HEIGHT):
		var row = []
		for x in range(ROOM_WIDTH):
			# 0 = empty space, 1 = wall, 2 = exit
			if x == 0 or x == ROOM_WIDTH - 1 or y == 0 or y == ROOM_HEIGHT - 1:
				row.append(1)  # Border walls
			else:
				row.append(0)  # Empty space
		room_grid.append(row)

func generate_simple_room():
	# Create simple Berzerk-style room layouts
	var room_type = randi() % 4  # 4 different room types

	match room_type:
		0:
			create_rectangular_room()
		1:
			create_l_shaped_room()
		2:
			create_cross_room()
		3:
			create_divided_room()

func create_rectangular_room():
	# Very simple room - just a few walls
	# Small horizontal wall
	for x in range(10, 20):
		room_grid[15][x] = 1

	# Small vertical wall
	for y in range(20, 30):
		room_grid[y][35] = 1

func create_l_shaped_room():
	# Simple L-shape
	for x in range(15, 25):
		room_grid[10][x] = 1
	for y in range(10, 20):
		room_grid[y][15] = 1

func create_cross_room():
	# Simple cross in center
	var center_x = ROOM_WIDTH / 2
	var center_y = ROOM_HEIGHT / 2

	# Short horizontal line
	for x in range(center_x - 5, center_x + 5):
		room_grid[center_y][x] = 1

	# Short vertical line
	for y in range(center_y - 3, center_y + 3):
		room_grid[y][center_x] = 1

func create_divided_room():
	# Simple vertical divider with gap
	var divider_x = ROOM_WIDTH / 2
	for y in range(5, ROOM_HEIGHT - 5):
		if y < 15 or y > 20:  # Leave gap in middle
			room_grid[y][divider_x] = 1



func create_exits():
	exits.clear()
	# No border walls - player exits by going off-screen
	# Remove border walls to allow exit

	# Clear top border in middle
	for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
		room_grid[0][x] = 0

	# Clear bottom border in middle
	for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
		room_grid[ROOM_HEIGHT - 1][x] = 0

	# Clear left border in middle
	for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
		room_grid[y][0] = 0

	# Clear right border in middle
	for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
		room_grid[y][ROOM_WIDTH - 1] = 0

func build_room_visuals():
	# Clear existing children
	for child in get_children():
		child.queue_free()

	var wall_count = 0
	# Create wall tiles
	for y in range(ROOM_HEIGHT):
		for x in range(ROOM_WIDTH):
			if room_grid[y][x] == 1:  # Wall
				create_wall_tile(x, y)
				wall_count += 1

	print("RoomGenerator: Created ", wall_count, " wall tiles")

func create_wall_tile(grid_x: int, grid_y: int):
	# Create a StaticBody2D for the wall
	var static_body = StaticBody2D.new()
	static_body.position = Vector2(grid_x * TILE_SIZE, grid_y * TILE_SIZE)
	static_body.collision_layer = 1  # Wall layer
	static_body.collision_mask = 0
	static_body.add_to_group("walls")

	# Add visual representation
	var wall = ColorRect.new()
	wall.size = Vector2(TILE_SIZE, TILE_SIZE)
	wall.color = Color.CYAN  # Bright cyan as per art direction
	static_body.add_child(wall)

	# Add collision shape
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(TILE_SIZE, TILE_SIZE)
	collision_shape.shape = rect_shape
	static_body.add_child(collision_shape)

	add_child(static_body)

func get_spawn_position() -> Vector2:
	# Return a safe spawn position (usually near a corner, away from walls)
	var spawn_positions = [
		Vector2(2 * TILE_SIZE, 2 * TILE_SIZE),  # Top-left
		Vector2((ROOM_WIDTH - 3) * TILE_SIZE, 2 * TILE_SIZE),  # Top-right
		Vector2(2 * TILE_SIZE, (ROOM_HEIGHT - 3) * TILE_SIZE),  # Bottom-left
		Vector2((ROOM_WIDTH - 3) * TILE_SIZE, (ROOM_HEIGHT - 3) * TILE_SIZE)  # Bottom-right
	]
	
	# Find the first spawn position that's not blocked
	for pos in spawn_positions:
		var grid_x = int(pos.x / TILE_SIZE)
		var grid_y = int(pos.y / TILE_SIZE)
		if room_grid[grid_y][grid_x] == 0:  # Empty space
			return pos
	
	# Fallback to center
	return Vector2(ROOM_WIDTH * TILE_SIZE / 2, ROOM_HEIGHT * TILE_SIZE / 2)

func get_exit_positions() -> Array:
	var exit_world_positions = []
	for exit in exits:
		exit_world_positions.append(Vector2(exit.x * TILE_SIZE, exit.y * TILE_SIZE))
	return exit_world_positions

func is_position_walkable(world_pos: Vector2) -> bool:
	var grid_x = int(world_pos.x / TILE_SIZE)
	var grid_y = int(world_pos.y / TILE_SIZE)
	
	if grid_x < 0 or grid_x >= ROOM_WIDTH or grid_y < 0 or grid_y >= ROOM_HEIGHT:
		return false
	
	return room_grid[grid_y][grid_x] != 1  # Not a wall
