# RoomGenerator.gd - Procedural maze generation for RobotWars
extends Node2D
class_name RoomGenerator

# Room dimensions - authentic Berzerk uses 3x5 grid of cells
const ROOM_WIDTH = 50   # 800 pixels / 16 = 50 tiles
const ROOM_HEIGHT = 37  # 600 pixels / 16 = 37.5, rounded down
const TILE_SIZE = 16

# Berzerk grid system - 3x5 cells, each ~160x120 pixels (10x7.5 tiles)
const GRID_COLS = 5
const GRID_ROWS = 3
const CELL_WIDTH = 10   # tiles per cell
const CELL_HEIGHT = 12  # tiles per cell

# We create wall tiles programmatically

# Room data
var room_grid: Array = []
var exits: Array = []

# Room generation settings
@export var min_path_width: int = 2
@export var max_wall_density: float = 0.4
@export var guaranteed_exits: int = 2

func _ready():
	print("RoomGenerator: Starting room generation...")
	generate_room()
	print("RoomGenerator: Room generation complete!")

func generate_room():
	# Initialize empty grid
	initialize_grid()

	# Generate simple Berzerk-style room layout
	generate_simple_room()

	# Create exits
	create_exits()

	# Ensure connectivity (basic check)
	ensure_basic_connectivity()

	# Build the visual room
	build_room_visuals()

	# Add grid visualization for debugging
	add_grid_visualization()

func initialize_grid():
	room_grid.clear()
	for y in range(ROOM_HEIGHT):
		var row = []
		for x in range(ROOM_WIDTH):
			# 0 = empty space, 1 = wall, 2 = exit
			if x == 0 or x == ROOM_WIDTH - 1 or y == 0 or y == ROOM_HEIGHT - 1:
				row.append(1)  # Border walls
			else:
				row.append(0)  # Empty space
		room_grid.append(row)

func generate_simple_room():
	# Generate authentic Berzerk maze using 3x5 grid system
	create_grid_based_maze()

func create_grid_based_maze():
	# Create maze walls that align to the 3x5 grid
	# Each cell is 10 tiles wide x 12 tiles tall

	# Randomly decide which internal grid lines to make into walls
	# Vertical grid lines (between columns)
	for col in range(1, GRID_COLS):  # 4 vertical lines between 5 columns
		var x_pos = col * CELL_WIDTH  # Grid line position

		# Randomly decide if this vertical line should have walls
		if randf() > 0.4:  # 60% chance of wall
			# Create wall segments with gaps for passages
			create_vertical_wall_with_gaps(x_pos)

	# Horizontal grid lines (between rows)
	for row in range(1, GRID_ROWS):  # 2 horizontal lines between 3 rows
		var y_pos = row * CELL_HEIGHT  # Grid line position

		# Randomly decide if this horizontal line should have walls
		if randf() > 0.4:  # 60% chance of wall
			# Create wall segments with gaps for passages
			create_horizontal_wall_with_gaps(y_pos)

func create_vertical_wall_with_gaps(x_pos: int):
	# Create a vertical wall along grid line with random gaps
	var wall_segments = []
	var current_start = 1

	# Create 1-3 wall segments with gaps between them
	var num_segments = randi_range(1, 3)
	var segment_length = (ROOM_HEIGHT - 2) / num_segments

	for i in range(num_segments):
		var start_y = current_start + i * segment_length
		var end_y = start_y + randi_range(segment_length / 2, segment_length - 2)

		# Ensure we don't go past room bounds
		end_y = min(end_y, ROOM_HEIGHT - 2)

		# Create wall segment
		for y in range(start_y, end_y):
			if y > 0 and y < ROOM_HEIGHT - 1:
				room_grid[y][x_pos] = 1

func create_horizontal_wall_with_gaps(y_pos: int):
	# Create a horizontal wall along grid line with random gaps
	var current_start = 1

	# Create 1-4 wall segments with gaps between them
	var num_segments = randi_range(1, 4)
	var segment_length = (ROOM_WIDTH - 2) / num_segments

	for i in range(num_segments):
		var start_x = current_start + i * segment_length
		var end_x = start_x + randi_range(segment_length / 2, segment_length - 2)

		# Ensure we don't go past room bounds
		end_x = min(end_x, ROOM_WIDTH - 2)

		# Create wall segment
		for x in range(start_x, end_x):
			if x > 0 and x < ROOM_WIDTH - 1:
				room_grid[y_pos][x] = 1



func create_exits():
	exits.clear()
	# No border walls - player exits by going off-screen
	# Remove border walls to allow exit

	# Clear top border in middle
	for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
		room_grid[0][x] = 0

	# Clear bottom border in middle
	for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
		room_grid[ROOM_HEIGHT - 1][x] = 0

	# Clear left border in middle
	for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
		room_grid[y][0] = 0

	# Clear right border in middle
	for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
		room_grid[y][ROOM_WIDTH - 1] = 0

func ensure_basic_connectivity():
	# Simple connectivity check - ensure center of each cell is reachable
	# This is a basic implementation - could be more sophisticated
	var center_positions = []

	# Get center of each grid cell
	for row in range(GRID_ROWS):
		for col in range(GRID_COLS):
			var center_x = col * CELL_WIDTH + CELL_WIDTH / 2
			var center_y = row * CELL_HEIGHT + CELL_HEIGHT / 2
			center_positions.append(Vector2i(center_x, center_y))

	# Ensure there's always a path from center cell to exits
	# Clear a minimal path if needed (simplified approach)
	var center_cell_x = ROOM_WIDTH / 2
	var center_cell_y = ROOM_HEIGHT / 2

	# Clear horizontal path to exits
	for x in range(center_cell_x - 5, center_cell_x + 5):
		if x > 0 and x < ROOM_WIDTH - 1:
			room_grid[ROOM_HEIGHT / 2][x] = 0

	# Clear vertical path to exits
	for y in range(center_cell_y - 5, center_cell_y + 5):
		if y > 0 and y < ROOM_HEIGHT - 1:
			room_grid[y][ROOM_WIDTH / 2] = 0

func add_grid_visualization():
	# Add visual grid lines to see the 3x5 cell structure (for debugging)
	# These are just visual - not collision

	# Vertical grid lines
	for col in range(1, GRID_COLS):
		var x_pos = col * CELL_WIDTH
		var line = ColorRect.new()
		line.size = Vector2(1, ROOM_HEIGHT * TILE_SIZE)
		line.position = Vector2(x_pos * TILE_SIZE, 0)
		line.color = Color(0, 1, 0, 0.3)  # Semi-transparent green
		add_child(line)

	# Horizontal grid lines
	for row in range(1, GRID_ROWS):
		var y_pos = row * CELL_HEIGHT
		var line = ColorRect.new()
		line.size = Vector2(ROOM_WIDTH * TILE_SIZE, 1)
		line.position = Vector2(0, y_pos * TILE_SIZE)
		line.color = Color(0, 1, 0, 0.3)  # Semi-transparent green
		add_child(line)

func build_room_visuals():
	# Clear existing children
	for child in get_children():
		child.queue_free()

	var wall_count = 0
	# Create wall tiles
	for y in range(ROOM_HEIGHT):
		for x in range(ROOM_WIDTH):
			if room_grid[y][x] == 1:  # Wall
				create_wall_tile(x, y)
				wall_count += 1

	print("RoomGenerator: Created ", wall_count, " wall tiles")

func create_wall_tile(grid_x: int, grid_y: int):
	# Create a StaticBody2D for the wall
	var static_body = StaticBody2D.new()
	static_body.position = Vector2(grid_x * TILE_SIZE, grid_y * TILE_SIZE)
	static_body.collision_layer = 1  # Wall layer
	static_body.collision_mask = 0
	static_body.add_to_group("walls")

	# Add visual representation
	var wall = ColorRect.new()
	wall.size = Vector2(TILE_SIZE, TILE_SIZE)
	wall.color = Color.CYAN  # Bright cyan as per art direction
	static_body.add_child(wall)

	# Add collision shape
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(TILE_SIZE, TILE_SIZE)
	collision_shape.shape = rect_shape
	static_body.add_child(collision_shape)

	add_child(static_body)

func get_spawn_position() -> Vector2:
	# Return center of middle cell (2,1) in the 3x5 grid - this is where player typically spawns
	var center_col = 2  # Middle column (0-4)
	var center_row = 1  # Middle row (0-2)

	var spawn_x = center_col * CELL_WIDTH + CELL_WIDTH / 2
	var spawn_y = center_row * CELL_HEIGHT + CELL_HEIGHT / 2

	return Vector2(spawn_x * TILE_SIZE, spawn_y * TILE_SIZE)

func get_exit_positions() -> Array:
	var exit_world_positions = []
	for exit in exits:
		exit_world_positions.append(Vector2(exit.x * TILE_SIZE, exit.y * TILE_SIZE))
	return exit_world_positions

func is_position_walkable(world_pos: Vector2) -> bool:
	var grid_x = int(world_pos.x / TILE_SIZE)
	var grid_y = int(world_pos.y / TILE_SIZE)
	
	if grid_x < 0 or grid_x >= ROOM_WIDTH or grid_y < 0 or grid_y >= ROOM_HEIGHT:
		return false
	
	return room_grid[grid_y][grid_x] != 1  # Not a wall
