# RoomGenerator.gd - Procedural maze generation for RobotWars
extends Node2D
class_name RoomGenerator

# Room dimensions (in tiles)
const ROOM_WIDTH = 50
const ROOM_HEIGHT = 37
const TILE_SIZE = 16

# We create wall tiles programmatically

# Room data
var room_grid: Array = []
var exits: Array = []

# Room generation settings
@export var min_path_width: int = 2
@export var max_wall_density: float = 0.4
@export var guaranteed_exits: int = 2

func _ready():
	print("RoomGenerator: Starting room generation...")
	generate_room()
	print("RoomGenerator: Room generation complete!")

func generate_room():
	# Initialize empty grid
	initialize_grid()
	
	# Generate maze structure
	generate_maze_walls()
	
	# Create exits
	create_exits()
	
	# Ensure all areas are reachable
	ensure_connectivity()
	
	# Build the visual room
	build_room_visuals()

func initialize_grid():
	room_grid.clear()
	for y in range(ROOM_HEIGHT):
		var row = []
		for x in range(ROOM_WIDTH):
			# 0 = empty space, 1 = wall, 2 = exit
			if x == 0 or x == ROOM_WIDTH - 1 or y == 0 or y == ROOM_HEIGHT - 1:
				row.append(1)  # Border walls
			else:
				row.append(0)  # Empty space
		room_grid.append(row)

func generate_maze_walls():
	# Simple maze generation - create some internal walls
	var wall_count = 0
	var max_walls = int(ROOM_WIDTH * ROOM_HEIGHT * max_wall_density)
	
	# Add some random wall clusters
	for i in range(max_walls / 10):  # Create wall clusters
		var cluster_x = randi_range(2, ROOM_WIDTH - 3)
		var cluster_y = randi_range(2, ROOM_HEIGHT - 3)
		var cluster_size = randi_range(3, 8)
		
		for j in range(cluster_size):
			var wall_x = cluster_x + randi_range(-2, 2)
			var wall_y = cluster_y + randi_range(-2, 2)
			
			if is_valid_wall_position(wall_x, wall_y):
				room_grid[wall_y][wall_x] = 1
				wall_count += 1
				
				if wall_count >= max_walls:
					break
		
		if wall_count >= max_walls:
			break
	
	# Add some corridor walls for more maze-like feel
	add_corridor_walls()

func add_corridor_walls():
	# Create some horizontal and vertical wall segments
	for i in range(5):  # 5 wall segments
		if randf() > 0.5:
			# Horizontal wall
			var start_x = randi_range(3, ROOM_WIDTH - 8)
			var y = randi_range(3, ROOM_HEIGHT - 4)
			var length = randi_range(3, 7)
			
			for x in range(start_x, min(start_x + length, ROOM_WIDTH - 2)):
				if is_valid_wall_position(x, y):
					room_grid[y][x] = 1
		else:
			# Vertical wall
			var x = randi_range(3, ROOM_WIDTH - 4)
			var start_y = randi_range(3, ROOM_HEIGHT - 8)
			var length = randi_range(3, 7)
			
			for y in range(start_y, min(start_y + length, ROOM_HEIGHT - 2)):
				if is_valid_wall_position(x, y):
					room_grid[y][x] = 1

func is_valid_wall_position(x: int, y: int) -> bool:
	# Check if we can place a wall here without blocking all paths
	if x <= 1 or x >= ROOM_WIDTH - 2 or y <= 1 or y >= ROOM_HEIGHT - 2:
		return false
	
	# Don't place walls too close to corners (player spawn areas)
	if (x < 4 and y < 4) or (x > ROOM_WIDTH - 5 and y < 4) or \
	   (x < 4 and y > ROOM_HEIGHT - 5) or (x > ROOM_WIDTH - 5 and y > ROOM_HEIGHT - 5):
		return false
	
	return room_grid[y][x] == 0

func create_exits():
	exits.clear()
	
	# Create exits on room borders
	var exit_positions = []
	
	# Top wall
	exit_positions.append(Vector2i(ROOM_WIDTH / 2, 0))
	# Bottom wall  
	exit_positions.append(Vector2i(ROOM_WIDTH / 2, ROOM_HEIGHT - 1))
	# Left wall
	exit_positions.append(Vector2i(0, ROOM_HEIGHT / 2))
	# Right wall
	exit_positions.append(Vector2i(ROOM_WIDTH - 1, ROOM_HEIGHT / 2))
	
	# Randomly select which exits to create
	exit_positions.shuffle()
	var num_exits = randi_range(guaranteed_exits, min(4, guaranteed_exits + 2))
	
	for i in range(num_exits):
		var exit_pos = exit_positions[i]
		room_grid[exit_pos.y][exit_pos.x] = 2
		exits.append(exit_pos)

func ensure_connectivity():
	# Simple connectivity check - make sure there's a path from center to all exits
	var center = Vector2i(ROOM_WIDTH / 2, ROOM_HEIGHT / 2)
	
	# Clear a guaranteed path from center to each exit
	for exit in exits:
		create_path_to_exit(center, exit)

func create_path_to_exit(start: Vector2i, end: Vector2i):
	# Simple pathfinding - create L-shaped path
	var current = start
	
	# Move horizontally first
	while current.x != end.x:
		if current.x < end.x:
			current.x += 1
		else:
			current.x -= 1
		
		# Clear the path (but don't overwrite exits)
		if room_grid[current.y][current.x] == 1:
			room_grid[current.y][current.x] = 0
	
	# Then move vertically
	while current.y != end.y:
		if current.y < end.y:
			current.y += 1
		else:
			current.y -= 1
		
		# Clear the path (but don't overwrite exits)
		if room_grid[current.y][current.x] == 1:
			room_grid[current.y][current.x] = 0

func build_room_visuals():
	# Clear existing children
	for child in get_children():
		child.queue_free()

	var wall_count = 0
	# Create wall tiles
	for y in range(ROOM_HEIGHT):
		for x in range(ROOM_WIDTH):
			if room_grid[y][x] == 1:  # Wall
				create_wall_tile(x, y)
				wall_count += 1

	print("RoomGenerator: Created ", wall_count, " wall tiles")

func create_wall_tile(grid_x: int, grid_y: int):
	# For now, create a simple colored rectangle
	# Later we'll replace this with proper wall tile sprites
	var wall = ColorRect.new()
	wall.size = Vector2(TILE_SIZE, TILE_SIZE)
	wall.position = Vector2(grid_x * TILE_SIZE, grid_y * TILE_SIZE)
	wall.color = Color.CYAN  # Bright cyan as per art direction
	
	# Add collision
	var static_body = StaticBody2D.new()
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(TILE_SIZE, TILE_SIZE)
	collision_shape.shape = rect_shape
	
	static_body.add_child(collision_shape)
	static_body.add_child(wall)
	static_body.position = Vector2(grid_x * TILE_SIZE, grid_y * TILE_SIZE)
	static_body.collision_layer = 1  # Wall layer
	static_body.collision_mask = 0
	static_body.add_to_group("walls")
	
	add_child(static_body)

func get_spawn_position() -> Vector2:
	# Return a safe spawn position (usually near a corner, away from walls)
	var spawn_positions = [
		Vector2(2 * TILE_SIZE, 2 * TILE_SIZE),  # Top-left
		Vector2((ROOM_WIDTH - 3) * TILE_SIZE, 2 * TILE_SIZE),  # Top-right
		Vector2(2 * TILE_SIZE, (ROOM_HEIGHT - 3) * TILE_SIZE),  # Bottom-left
		Vector2((ROOM_WIDTH - 3) * TILE_SIZE, (ROOM_HEIGHT - 3) * TILE_SIZE)  # Bottom-right
	]
	
	# Find the first spawn position that's not blocked
	for pos in spawn_positions:
		var grid_x = int(pos.x / TILE_SIZE)
		var grid_y = int(pos.y / TILE_SIZE)
		if room_grid[grid_y][grid_x] == 0:  # Empty space
			return pos
	
	# Fallback to center
	return Vector2(ROOM_WIDTH * TILE_SIZE / 2, ROOM_HEIGHT * TILE_SIZE / 2)

func get_exit_positions() -> Array:
	var exit_world_positions = []
	for exit in exits:
		exit_world_positions.append(Vector2(exit.x * TILE_SIZE, exit.y * TILE_SIZE))
	return exit_world_positions

func is_position_walkable(world_pos: Vector2) -> bool:
	var grid_x = int(world_pos.x / TILE_SIZE)
	var grid_y = int(world_pos.y / TILE_SIZE)
	
	if grid_x < 0 or grid_x >= ROOM_WIDTH or grid_y < 0 or grid_y >= ROOM_HEIGHT:
		return false
	
	return room_grid[grid_y][grid_x] != 1  # Not a wall
