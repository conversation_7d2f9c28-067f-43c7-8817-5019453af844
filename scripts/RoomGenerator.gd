# RoomGenerator.gd - Procedural maze generation for RobotWars
extends Node2D
class_name RoomGenerator

# Room dimensions - authentic Berzerk uses 3x5 grid of cells
const ROOM_WIDTH = 50   # 800 pixels / 16 = 50 tiles
const ROOM_HEIGHT = 37  # 600 pixels / 16 = 37.5, rounded down
const TILE_SIZE = 16

# Berzerk grid system - 3x5 cells, each ~160x120 pixels (10x7.5 tiles)
const GRID_COLS = 5
const GRID_ROWS = 3
const CELL_WIDTH = 10   # tiles per cell
const CELL_HEIGHT = 12  # tiles per cell

# We create wall tiles programmatically

# Room data
var room_grid: Array = []
var exits: Array = []

# Room coordinate system (for seeding and connections)
var room_x: int = 0
var room_y: int = 0
var room_level: int = 1
var entry_direction: String = ""  # "north", "south", "east", "west", or ""

# Room generation settings
var rng: RandomNumberGenerator

func _ready():
	# Don't auto-generate - wait for setup_room call
	pass

func setup_room(x: int, y: int, level: int, entry_dir: String = ""):
	room_x = x
	room_y = y
	room_level = level
	entry_direction = entry_dir

	print("RoomGenerator: Setting up room (", x, ",", y, ") level ", level, " entry: ", entry_dir)
	generate_room()
	print("RoomGenerator: Room generation complete!")

func generate_room():
	# Initialize coordinate-based RNG for consistency
	setup_coordinate_rng()

	# Initialize empty grid
	initialize_grid()

	# Generate maze using coordinate-based seeding
	generate_coordinate_based_maze()

	# Create exits based on room connections and entry direction
	create_logical_exits()

	# Ensure exits are reachable
	ensure_exit_connectivity()

	# Build the visual room
	build_room_visuals()

	# Add grid visualization for debugging
	add_grid_visualization()

func setup_coordinate_rng():
	# Use room coordinates as 16-bit seed for consistent generation
	rng = RandomNumberGenerator.new()
	var seed_value = (room_x & 0xFF) | ((room_y & 0xFF) << 8)
	rng.seed = seed_value
	print("Room seed: ", seed_value, " for coordinates (", room_x, ",", room_y, ")")

func initialize_grid():
	room_grid.clear()
	for y in range(ROOM_HEIGHT):
		var row = []
		for x in range(ROOM_WIDTH):
			# 0 = empty space, 1 = wall, 2 = exit
			if x == 0 or x == ROOM_WIDTH - 1 or y == 0 or y == ROOM_HEIGHT - 1:
				row.append(1)  # Border walls
			else:
				row.append(0)  # Empty space
		room_grid.append(row)

func generate_coordinate_based_maze():
	# SIMPLE APPROACH: Start with all cells connected, then selectively add walls
	# This guarantees no isolation because we never break connectivity

	# Generate some walls using coordinate-based randomness, but very conservatively
	create_minimal_walls()

func create_minimal_walls():
	# Create a minimal number of walls that add strategic interest without isolation
	# Use coordinate-based seeding for consistency

	var complexity_factor = get_complexity_factor()
	var wall_count = 0
	var max_walls = int(2 + complexity_factor * 3)  # 2-5 walls max

	# Generate wall candidates using coordinate-based randomness
	var wall_candidates = []

	# Vertical wall candidates (4 possible positions)
	for col in range(1, GRID_COLS):
		for row in range(GRID_ROWS):
			var wall_chance = rng.randf()
			if wall_chance < 0.3:  # 30% chance
				wall_candidates.append({"type": "vertical", "col": col, "row": row, "chance": wall_chance})

	# Horizontal wall candidates (2 possible positions)
	for row in range(1, GRID_ROWS):
		for col in range(GRID_COLS):
			var wall_chance = rng.randf()
			if wall_chance < 0.25:  # 25% chance
				wall_candidates.append({"type": "horizontal", "row": row, "col": col, "chance": wall_chance})

	# Sort by chance and take only the best candidates
	wall_candidates.sort_custom(func(a, b): return a.chance < b.chance)

	# Place walls conservatively
	for candidate in wall_candidates:
		if wall_count >= max_walls:
			break

		# Only place wall if it doesn't create isolation
		if can_place_wall_safely(candidate):
			if candidate.type == "vertical":
				create_vertical_gridline_wall(candidate.col, candidate.row)
			else:
				create_horizontal_gridline_wall(candidate.row, candidate.col)
			wall_count += 1
			print("Placed safe wall: ", candidate)

func can_place_wall_safely(wall_candidate: Dictionary) -> bool:
	# Check if placing this wall would create isolation
	# For now, use simple heuristics to avoid obvious isolation

	if wall_candidate.type == "vertical":
		var col = wall_candidate.col
		var row = wall_candidate.row

		# Don't place vertical walls that would completely separate left/right sides
		if col == 2 or col == 3:  # Middle columns
			return false

		# Don't place walls in corner areas
		if (col == 1 and (row == 0 or row == 2)) or (col == 4 and (row == 0 or row == 2)):
			return false

	else:  # horizontal
		var row = wall_candidate.row
		var col = wall_candidate.col

		# Don't place horizontal walls that would completely separate top/bottom
		if row == 1:  # Middle row
			return false

		# Don't place walls near exits
		if col >= 2 and col <= 3:  # Near center where exits are
			return false

	return true

func ensure_exit_connectivity():
	# Make sure there are clear paths to all exits
	# This is a safety measure to prevent exit blocking

	var center_x = ROOM_WIDTH / 2
	var center_y = ROOM_HEIGHT / 2

	# Clear horizontal corridor through center (for left/right exits)
	for x in range(center_x - 8, center_x + 9):
		if x >= 0 and x < ROOM_WIDTH:
			room_grid[center_y][x] = 0
			room_grid[center_y - 1][x] = 0
			room_grid[center_y + 1][x] = 0

	# Clear vertical corridor through center (for top/bottom exits)
	for y in range(center_y - 6, center_y + 7):
		if y >= 0 and y < ROOM_HEIGHT:
			room_grid[y][center_x] = 0
			room_grid[y][center_x - 1] = 0
			room_grid[y][center_x + 1] = 0

	print("Ensured exit connectivity with cross-shaped corridor")



func get_complexity_factor() -> float:
	# Increase maze complexity with progression
	return min(room_level * 0.1, 1.0)  # 0.0 to 1.0 based on level

func create_vertical_gridline_wall(col: int, row: int):
	# Create a full-height wall on the vertical gridline
	var x_pos = col * CELL_WIDTH
	var start_y = row * CELL_HEIGHT
	var end_y = (row + 1) * CELL_HEIGHT

	for y in range(start_y, end_y):
		if y >= 0 and y < ROOM_HEIGHT and x_pos >= 0 and x_pos < ROOM_WIDTH:
			room_grid[y][x_pos] = 1

func create_horizontal_gridline_wall(row: int, col: int):
	# Create a full-width wall on the horizontal gridline
	var y_pos = row * CELL_HEIGHT
	var start_x = col * CELL_WIDTH
	var end_x = (col + 1) * CELL_WIDTH

	for x in range(start_x, end_x):
		if x >= 0 and x < ROOM_WIDTH and y_pos >= 0 and y_pos < ROOM_HEIGHT:
			room_grid[y_pos][x] = 1

func create_cell_connectivity_map() -> Array:
	# Create a 3x5 array tracking which directions each cell connects to
	var connections = []

	for row in range(GRID_ROWS):
		var row_connections = []
		for col in range(GRID_COLS):
			# Each cell tracks connections: [north, south, east, west]
			var cell_connections = [false, false, false, false]
			row_connections.append(cell_connections)
		connections.append(row_connections)

	# Ensure every cell has at least 2 connections (no dead ends)
	for row in range(GRID_ROWS):
		for col in range(GRID_COLS):
			ensure_minimum_connections(connections, col, row)

	return connections

func ensure_minimum_connections(connections: Array, col: int, row: int):
	var cell = connections[row][col]
	var connection_count = 0

	# Count existing connections
	for i in range(4):
		if cell[i]:
			connection_count += 1

	# Ensure at least 2 connections per cell
	while connection_count < 2:
		var direction = randi() % 4
		var can_connect = false

		match direction:
			0: # North
				can_connect = row > 0
			1: # South
				can_connect = row < GRID_ROWS - 1
			2: # East
				can_connect = col < GRID_COLS - 1
			3: # West
				can_connect = col > 0

		if can_connect and not cell[direction]:
			cell[direction] = true

			# Also set the reciprocal connection
			match direction:
				0: # North - connect to south of cell above
					connections[row - 1][col][1] = true
				1: # South - connect to north of cell below
					connections[row + 1][col][0] = true
				2: # East - connect to west of cell to right
					connections[row][col + 1][3] = true
				3: # West - connect to east of cell to left
					connections[row][col - 1][2] = true

			connection_count += 1

func build_gridline_walls_from_connectivity(connections: Array):
	# Build walls ONLY on gridlines - full length/height of grid cells

	# Vertical gridline walls (between columns)
	for col in range(1, GRID_COLS):  # 4 vertical gridlines between 5 columns
		var x_pos = col * CELL_WIDTH

		# Check each row to see if this vertical gridline should have a wall
		for row in range(GRID_ROWS):
			var left_cell = connections[row][col - 1]
			var right_cell = connections[row][col]

			# If cells are NOT connected, place wall for the full height of this grid cell
			if not left_cell[2] or not right_cell[3]:  # East/West connection
				# Place wall for the FULL height of this grid cell
				var start_y = row * CELL_HEIGHT
				var end_y = (row + 1) * CELL_HEIGHT

				for y in range(start_y, end_y):
					if y >= 0 and y < ROOM_HEIGHT and x_pos >= 0 and x_pos < ROOM_WIDTH:
						room_grid[y][x_pos] = 1

	# Horizontal gridline walls (between rows)
	for row in range(1, GRID_ROWS):  # 2 horizontal gridlines between 3 rows
		var y_pos = row * CELL_HEIGHT

		# Check each column to see if this horizontal gridline should have a wall
		for col in range(GRID_COLS):
			var top_cell = connections[row - 1][col]
			var bottom_cell = connections[row][col]

			# If cells are NOT connected, place wall for the full width of this grid cell
			if not top_cell[1] or not bottom_cell[0]:  # North/South connection
				# Place wall for the FULL width of this grid cell
				var start_x = col * CELL_WIDTH
				var end_x = (col + 1) * CELL_WIDTH

				for x in range(start_x, end_x):
					if x >= 0 and x < ROOM_WIDTH and y_pos >= 0 and y_pos < ROOM_HEIGHT:
						room_grid[y_pos][x] = 1





func create_logical_exits():
	exits.clear()

	# Create 2-3 exits, but never in the direction player came from
	var possible_directions = ["north", "south", "east", "west"]
	var blocked_direction = get_blocked_direction()

	# Remove the blocked direction
	if blocked_direction != "":
		possible_directions.erase(blocked_direction)

	# Choose 2-3 exits from remaining directions
	var num_exits = rng.randi_range(2, 3)
	num_exits = min(num_exits, possible_directions.size())

	possible_directions.shuffle()  # Use built-in shuffle

	for i in range(num_exits):
		var direction = possible_directions[i]
		create_exit_in_direction(direction)
		exits.append(direction)

	print("Created exits: ", exits, " (blocked: ", blocked_direction, ")")

func get_blocked_direction() -> String:
	# Block the direction opposite to where player entered
	match entry_direction:
		"north": return "south"  # If entered from north, block south exit
		"south": return "north"  # If entered from south, block north exit
		"east": return "west"    # If entered from east, block west exit
		"west": return "east"    # If entered from west, block east exit
		_: return ""             # First room - no blocked direction

func create_exit_in_direction(direction: String):
	# Create an exit by clearing border walls in the specified direction
	match direction:
		"north":
			# Clear top border in middle
			for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
				if x >= 0 and x < ROOM_WIDTH:
					room_grid[0][x] = 0
		"south":
			# Clear bottom border in middle
			for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
				if x >= 0 and x < ROOM_WIDTH:
					room_grid[ROOM_HEIGHT - 1][x] = 0
		"west":
			# Clear left border in middle
			for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
				if y >= 0 and y < ROOM_HEIGHT:
					room_grid[y][0] = 0
		"east":
			# Clear right border in middle
			for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
				if y >= 0 and y < ROOM_HEIGHT:
					room_grid[y][ROOM_WIDTH - 1] = 0

func ensure_basic_connectivity():
	# The connectivity is now ensured by the cell connection system
	# Just make sure there are clear paths to the screen exits

	# Clear paths to screen exits (top, bottom, left, right)
	var center_x = ROOM_WIDTH / 2
	var center_y = ROOM_HEIGHT / 2

	# Horizontal path through middle
	for x in range(center_x - 3, center_x + 4):
		if x > 0 and x < ROOM_WIDTH - 1:
			room_grid[center_y][x] = 0

	# Vertical path through middle
	for y in range(center_y - 3, center_y + 4):
		if y > 0 and y < ROOM_HEIGHT - 1:
			room_grid[y][center_x] = 0

func add_grid_visualization():
	# Add visual grid lines to see the 3x5 cell structure (for debugging)
	# These are just visual - not collision

	# Vertical grid lines
	for col in range(1, GRID_COLS):
		var x_pos = col * CELL_WIDTH
		var line = ColorRect.new()
		line.size = Vector2(1, ROOM_HEIGHT * TILE_SIZE)
		line.position = Vector2(x_pos * TILE_SIZE, 0)
		line.color = Color(0, 1, 0, 0.3)  # Semi-transparent green
		add_child(line)

	# Horizontal grid lines
	for row in range(1, GRID_ROWS):
		var y_pos = row * CELL_HEIGHT
		var line = ColorRect.new()
		line.size = Vector2(ROOM_WIDTH * TILE_SIZE, 1)
		line.position = Vector2(0, y_pos * TILE_SIZE)
		line.color = Color(0, 1, 0, 0.3)  # Semi-transparent green
		add_child(line)

func build_room_visuals():
	# Clear existing children
	for child in get_children():
		child.queue_free()

	var wall_count = 0
	# Create wall tiles
	for y in range(ROOM_HEIGHT):
		for x in range(ROOM_WIDTH):
			if room_grid[y][x] == 1:  # Wall
				create_wall_tile(x, y)
				wall_count += 1

	print("RoomGenerator: Created ", wall_count, " wall tiles")

func create_wall_tile(grid_x: int, grid_y: int):
	# Create a StaticBody2D for the wall
	var static_body = StaticBody2D.new()
	static_body.position = Vector2(grid_x * TILE_SIZE, grid_y * TILE_SIZE)
	static_body.collision_layer = 1  # Wall layer
	static_body.collision_mask = 0
	static_body.add_to_group("walls")

	# Add visual representation
	var wall = ColorRect.new()
	wall.size = Vector2(TILE_SIZE, TILE_SIZE)
	wall.color = Color.CYAN  # Bright cyan as per art direction
	static_body.add_child(wall)

	# Add collision shape
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(TILE_SIZE, TILE_SIZE)
	collision_shape.shape = rect_shape
	static_body.add_child(collision_shape)

	add_child(static_body)

func get_spawn_position() -> Vector2:
	# Player spawns in a corner cell based on entry direction
	var spawn_col = 0
	var spawn_row = 0

	match entry_direction:
		"west":   # Entered from west, spawn in east side
			spawn_col = 4  # Rightmost column
			spawn_row = rng.randi_range(0, 2)  # Any row
		"east":   # Entered from east, spawn in west side
			spawn_col = 0  # Leftmost column
			spawn_row = rng.randi_range(0, 2)  # Any row
		"north":  # Entered from north, spawn in south side
			spawn_col = rng.randi_range(0, 4)  # Any column
			spawn_row = 2  # Bottom row
		"south":  # Entered from south, spawn in north side
			spawn_col = rng.randi_range(0, 4)  # Any column
			spawn_row = 0  # Top row
		_:        # First room - spawn in corner
			spawn_col = 0  # Top-left corner
			spawn_row = 0

	var spawn_x = spawn_col * CELL_WIDTH + CELL_WIDTH / 2
	var spawn_y = spawn_row * CELL_HEIGHT + CELL_HEIGHT / 2

	return Vector2(spawn_x * TILE_SIZE, spawn_y * TILE_SIZE)

func get_player_spawn_cell() -> Vector2i:
	# Return which grid cell the player spawns in (for robot avoidance)
	match entry_direction:
		"west": return Vector2i(4, rng.randi_range(0, 2))
		"east": return Vector2i(0, rng.randi_range(0, 2))
		"north": return Vector2i(rng.randi_range(0, 4), 2)
		"south": return Vector2i(rng.randi_range(0, 4), 0)
		_: return Vector2i(0, 0)  # First room corner

func get_exit_positions() -> Array:
	var exit_world_positions = []
	for exit in exits:
		exit_world_positions.append(Vector2(exit.x * TILE_SIZE, exit.y * TILE_SIZE))
	return exit_world_positions

func is_position_walkable(world_pos: Vector2) -> bool:
	var grid_x = int(world_pos.x / TILE_SIZE)
	var grid_y = int(world_pos.y / TILE_SIZE)
	
	if grid_x < 0 or grid_x >= ROOM_WIDTH or grid_y < 0 or grid_y >= ROOM_HEIGHT:
		return false
	
	return room_grid[grid_y][grid_x] != 1  # Not a wall
