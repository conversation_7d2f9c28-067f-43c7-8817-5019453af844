# RoomGenerator.gd - Procedural maze generation for RobotWars
extends Node2D
class_name RoomGenerator

# Room dimensions - authentic Berzerk uses 3x5 grid of cells
const ROOM_WIDTH = 50   # 800 pixels / 16 = 50 tiles
const ROOM_HEIGHT = 37  # 600 pixels / 16 = 37.5, rounded down
const TILE_SIZE = 16

# Berzerk grid system - 3x5 cells, each ~160x120 pixels (10x7.5 tiles)
const GRID_COLS = 5
const GRID_ROWS = 3
const CELL_WIDTH = 10   # tiles per cell
const CELL_HEIGHT = 12  # tiles per cell

# We create wall tiles programmatically

# Room data
var room_grid: Array = []
var exits: Array = []

# Room generation settings
@export var min_path_width: int = 2
@export var max_wall_density: float = 0.4
@export var guaranteed_exits: int = 2

func _ready():
	print("RoomGenerator: Starting room generation...")
	generate_room()
	print("RoomGenerator: Room generation complete!")

func generate_room():
	# Initialize empty grid
	initialize_grid()

	# Generate simple Berzerk-style room layout
	generate_simple_room()

	# Create exits
	create_exits()

	# Ensure connectivity (basic check)
	ensure_basic_connectivity()

	# Build the visual room
	build_room_visuals()

	# Add grid visualization for debugging
	add_grid_visualization()

func initialize_grid():
	room_grid.clear()
	for y in range(ROOM_HEIGHT):
		var row = []
		for x in range(ROOM_WIDTH):
			# 0 = empty space, 1 = wall, 2 = exit
			if x == 0 or x == ROOM_WIDTH - 1 or y == 0 or y == ROOM_HEIGHT - 1:
				row.append(1)  # Border walls
			else:
				row.append(0)  # Empty space
		room_grid.append(row)

func generate_simple_room():
	# Generate authentic Berzerk maze using 3x5 grid system
	create_grid_based_maze()

func create_grid_based_maze():
	# Create authentic Berzerk maze ensuring no isolated cells

	# Step 1: Create a connectivity map for each cell
	var cell_connections = create_cell_connectivity_map()

	# Step 2: Build walls based on connectivity, ensuring each cell has access
	build_walls_from_connectivity(cell_connections)

	# Step 3: Add some interior walls within cells for cover
	add_interior_walls()

func create_cell_connectivity_map() -> Array:
	# Create a 3x5 array tracking which directions each cell connects to
	var connections = []

	for row in range(GRID_ROWS):
		var row_connections = []
		for col in range(GRID_COLS):
			# Each cell tracks connections: [north, south, east, west]
			var cell_connections = [false, false, false, false]
			row_connections.append(cell_connections)
		connections.append(row_connections)

	# Ensure every cell has at least 2 connections (no dead ends)
	for row in range(GRID_ROWS):
		for col in range(GRID_COLS):
			ensure_minimum_connections(connections, col, row)

	return connections

func ensure_minimum_connections(connections: Array, col: int, row: int):
	var cell = connections[row][col]
	var connection_count = 0

	# Count existing connections
	for i in range(4):
		if cell[i]:
			connection_count += 1

	# Ensure at least 2 connections per cell
	while connection_count < 2:
		var direction = randi() % 4
		var can_connect = false

		match direction:
			0: # North
				can_connect = row > 0
			1: # South
				can_connect = row < GRID_ROWS - 1
			2: # East
				can_connect = col < GRID_COLS - 1
			3: # West
				can_connect = col > 0

		if can_connect and not cell[direction]:
			cell[direction] = true

			# Also set the reciprocal connection
			match direction:
				0: # North - connect to south of cell above
					connections[row - 1][col][1] = true
				1: # South - connect to north of cell below
					connections[row + 1][col][0] = true
				2: # East - connect to west of cell to right
					connections[row][col + 1][3] = true
				3: # West - connect to east of cell to left
					connections[row][col - 1][2] = true

			connection_count += 1

func build_walls_from_connectivity(connections: Array):
	# Build walls based on the connectivity map

	# Vertical walls (between columns)
	for col in range(1, GRID_COLS):
		var x_pos = col * CELL_WIDTH

		for row in range(GRID_ROWS):
			# Check if cells on left and right are connected
			var left_cell = connections[row][col - 1]
			var right_cell = connections[row][col]

			# If left cell doesn't connect east OR right cell doesn't connect west, add wall
			if not left_cell[2] or not right_cell[3]:
				# Add wall segment for this row
				var start_y = row * CELL_HEIGHT + 1
				var end_y = (row + 1) * CELL_HEIGHT - 1

				for y in range(start_y, end_y):
					if y > 0 and y < ROOM_HEIGHT - 1:
						room_grid[y][x_pos] = 1

	# Horizontal walls (between rows)
	for row in range(1, GRID_ROWS):
		var y_pos = row * CELL_HEIGHT

		for col in range(GRID_COLS):
			# Check if cells above and below are connected
			var top_cell = connections[row - 1][col]
			var bottom_cell = connections[row][col]

			# If top cell doesn't connect south OR bottom cell doesn't connect north, add wall
			if not top_cell[1] or not bottom_cell[0]:
				# Add wall segment for this column
				var start_x = col * CELL_WIDTH + 1
				var end_x = (col + 1) * CELL_WIDTH - 1

				for x in range(start_x, end_x):
					if x > 0 and x < ROOM_WIDTH - 1:
						room_grid[y_pos][x] = 1

func add_interior_walls():
	# Add some interior walls within cells for tactical cover
	# These don't block cell-to-cell movement, just provide cover

	for row in range(GRID_ROWS):
		for col in range(GRID_COLS):
			# 30% chance to add interior wall in each cell
			if randf() < 0.3:
				add_interior_wall_to_cell(col, row)

func add_interior_wall_to_cell(col: int, row: int):
	# Add a small interior wall within the cell for cover
	var cell_start_x = col * CELL_WIDTH + 2
	var cell_start_y = row * CELL_HEIGHT + 2
	var cell_end_x = (col + 1) * CELL_WIDTH - 2
	var cell_end_y = (row + 1) * CELL_HEIGHT - 2

	# Choose wall type: horizontal or vertical
	if randf() > 0.5:
		# Horizontal interior wall
		var wall_y = randi_range(cell_start_y + 2, cell_end_y - 2)
		var wall_start_x = randi_range(cell_start_x, cell_start_x + 3)
		var wall_end_x = randi_range(cell_end_x - 3, cell_end_x)

		for x in range(wall_start_x, wall_end_x):
			if x > 0 and x < ROOM_WIDTH - 1:
				room_grid[wall_y][x] = 1
	else:
		# Vertical interior wall
		var wall_x = randi_range(cell_start_x + 2, cell_end_x - 2)
		var wall_start_y = randi_range(cell_start_y, cell_start_y + 2)
		var wall_end_y = randi_range(cell_end_y - 2, cell_end_y)

		for y in range(wall_start_y, wall_end_y):
			if y > 0 and y < ROOM_HEIGHT - 1:
				room_grid[y][wall_x] = 1





func create_exits():
	exits.clear()
	# No border walls - player exits by going off-screen
	# Remove border walls to allow exit

	# Clear top border in middle
	for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
		room_grid[0][x] = 0

	# Clear bottom border in middle
	for x in range(ROOM_WIDTH / 2 - 2, ROOM_WIDTH / 2 + 3):
		room_grid[ROOM_HEIGHT - 1][x] = 0

	# Clear left border in middle
	for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
		room_grid[y][0] = 0

	# Clear right border in middle
	for y in range(ROOM_HEIGHT / 2 - 2, ROOM_HEIGHT / 2 + 3):
		room_grid[y][ROOM_WIDTH - 1] = 0

func ensure_basic_connectivity():
	# The connectivity is now ensured by the cell connection system
	# Just make sure there are clear paths to the screen exits

	# Clear paths to screen exits (top, bottom, left, right)
	var center_x = ROOM_WIDTH / 2
	var center_y = ROOM_HEIGHT / 2

	# Horizontal path through middle
	for x in range(center_x - 3, center_x + 4):
		if x > 0 and x < ROOM_WIDTH - 1:
			room_grid[center_y][x] = 0

	# Vertical path through middle
	for y in range(center_y - 3, center_y + 4):
		if y > 0 and y < ROOM_HEIGHT - 1:
			room_grid[y][center_x] = 0

func add_grid_visualization():
	# Add visual grid lines to see the 3x5 cell structure (for debugging)
	# These are just visual - not collision

	# Vertical grid lines
	for col in range(1, GRID_COLS):
		var x_pos = col * CELL_WIDTH
		var line = ColorRect.new()
		line.size = Vector2(1, ROOM_HEIGHT * TILE_SIZE)
		line.position = Vector2(x_pos * TILE_SIZE, 0)
		line.color = Color(0, 1, 0, 0.3)  # Semi-transparent green
		add_child(line)

	# Horizontal grid lines
	for row in range(1, GRID_ROWS):
		var y_pos = row * CELL_HEIGHT
		var line = ColorRect.new()
		line.size = Vector2(ROOM_WIDTH * TILE_SIZE, 1)
		line.position = Vector2(0, y_pos * TILE_SIZE)
		line.color = Color(0, 1, 0, 0.3)  # Semi-transparent green
		add_child(line)

func build_room_visuals():
	# Clear existing children
	for child in get_children():
		child.queue_free()

	var wall_count = 0
	# Create wall tiles
	for y in range(ROOM_HEIGHT):
		for x in range(ROOM_WIDTH):
			if room_grid[y][x] == 1:  # Wall
				create_wall_tile(x, y)
				wall_count += 1

	print("RoomGenerator: Created ", wall_count, " wall tiles")

func create_wall_tile(grid_x: int, grid_y: int):
	# Create a StaticBody2D for the wall
	var static_body = StaticBody2D.new()
	static_body.position = Vector2(grid_x * TILE_SIZE, grid_y * TILE_SIZE)
	static_body.collision_layer = 1  # Wall layer
	static_body.collision_mask = 0
	static_body.add_to_group("walls")

	# Add visual representation
	var wall = ColorRect.new()
	wall.size = Vector2(TILE_SIZE, TILE_SIZE)
	wall.color = Color.CYAN  # Bright cyan as per art direction
	static_body.add_child(wall)

	# Add collision shape
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(TILE_SIZE, TILE_SIZE)
	collision_shape.shape = rect_shape
	static_body.add_child(collision_shape)

	add_child(static_body)

func get_spawn_position() -> Vector2:
	# Return center of middle cell (2,1) in the 3x5 grid - this is where player typically spawns
	var center_col = 2  # Middle column (0-4)
	var center_row = 1  # Middle row (0-2)

	var spawn_x = center_col * CELL_WIDTH + CELL_WIDTH / 2
	var spawn_y = center_row * CELL_HEIGHT + CELL_HEIGHT / 2

	return Vector2(spawn_x * TILE_SIZE, spawn_y * TILE_SIZE)

func get_exit_positions() -> Array:
	var exit_world_positions = []
	for exit in exits:
		exit_world_positions.append(Vector2(exit.x * TILE_SIZE, exit.y * TILE_SIZE))
	return exit_world_positions

func is_position_walkable(world_pos: Vector2) -> bool:
	var grid_x = int(world_pos.x / TILE_SIZE)
	var grid_y = int(world_pos.y / TILE_SIZE)
	
	if grid_x < 0 or grid_x >= ROOM_WIDTH or grid_y < 0 or grid_y >= ROOM_HEIGHT:
		return false
	
	return room_grid[grid_y][grid_x] != 1  # Not a wall
