# Main.gd - Main game scene controller
extends Node2D

# Scene references
@onready var room_generator: RoomGenerator
@onready var player: Player
@onready var camera: Camera2D

# Room coordinate system
var current_room_x: int = 0
var current_room_y: int = 0
var current_level: int = 1

func _ready():
	print("Main: Starting game initialization...")
	# Initialize the game
	setup_camera()
	print("Main: Camera setup complete")
	create_room()
	print("Main: Room creation complete")
	spawn_player()
	print("Main: Player spawn complete")
	spawn_robots()
	print("Main: Robot spawn complete")
	setup_hud()
	print("Main: HUD setup complete")

	# Start the game
	GameManager.start_new_game()
	print("Main: Game started!")

func setup_hud():
	# Connect GameManager signals to HUD
	var hud = $CanvasLayer/HUD
	var score_label = hud.get_node("ScoreLabel")
	var lives_label = hud.get_node("LivesLabel")
	var room_label = hud.get_node("RoomLabel")

	# Connect signals
	GameManager.score_changed.connect(_on_score_changed)
	GameManager.lives_changed.connect(_on_lives_changed)
	GameManager.room_changed.connect(_on_room_changed)

func _on_score_changed(new_score: int):
	var score_label = $CanvasLayer/HUD/ScoreLabel
	score_label.text = "SCORE: " + str(new_score)

func _on_lives_changed(new_lives: int):
	var lives_label = $CanvasLayer/HUD/LivesLabel
	lives_label.text = "LIVES: " + str(new_lives)

func _on_room_changed(new_room: int):
	var room_label = $CanvasLayer/HUD/RoomLabel
	room_label.text = "ROOM " + str(new_room)

func check_screen_exit():
	var player_pos = player.global_position
	var exit_direction = ""
	var new_player_pos = Vector2.ZERO

	# Check if player has exited screen boundaries
	if player_pos.x < 0:
		exit_direction = "left"
		new_player_pos = Vector2(784, player_pos.y)  # Enter from right side
	elif player_pos.x > 800:
		exit_direction = "right"
		new_player_pos = Vector2(16, player_pos.y)   # Enter from left side
	elif player_pos.y < 0:
		exit_direction = "top"
		new_player_pos = Vector2(player_pos.x, 584)  # Enter from bottom
	elif player_pos.y > 600:
		exit_direction = "bottom"
		new_player_pos = Vector2(player_pos.x, 16)   # Enter from top

	if exit_direction != "":
		transition_to_new_room(exit_direction, new_player_pos)

func transition_to_new_room(exit_direction: String, new_player_pos: Vector2):
	print("Player exited ", exit_direction, " - transitioning to new room")

	# Calculate new room coordinates
	var new_room_x = current_room_x
	var new_room_y = current_room_y
	var entry_direction = ""

	match exit_direction:
		"right":
			new_room_x += 1
			entry_direction = "west"
		"left":
			new_room_x -= 1
			entry_direction = "east"
		"top":
			new_room_y -= 1
			entry_direction = "south"
		"bottom":
			new_room_y += 1
			entry_direction = "north"

	# Update current coordinates
	current_room_x = new_room_x
	current_room_y = new_room_y

	# Freeze player during transition
	player.is_alive = false

	# Clear existing robots
	var robots = get_tree().get_nodes_in_group("robots")
	for robot in robots:
		robot.queue_free()

	# Generate new room with logical connections
	room_generator.queue_free()
	room_generator = RoomGenerator.new()
	add_child(room_generator)
	room_generator.setup_room(current_room_x, current_room_y, current_level, entry_direction)

	# Spawn new robots (avoid corner where player spawns)
	spawn_robots()

	# Position player at entry point
	player.global_position = new_player_pos
	player.is_alive = true

	# Update room counter
	GameManager.next_room()

	print("New room loaded at (", current_room_x, ",", current_room_y, ") player at: ", new_player_pos)

func setup_camera():
	# Create camera for pixel-perfect rendering - fixed position to show entire room
	camera = Camera2D.new()
	camera.enabled = true
	camera.zoom = Vector2.ONE  # 1:1 pixel ratio
	camera.position = Vector2(400, 300)  # Center of 800x600 screen
	add_child(camera)

func create_room():
	# Create room generator with coordinate-based seeding
	room_generator = RoomGenerator.new()
	add_child(room_generator)

	# Setup room with coordinates (start at 0,0 with no entry direction)
	room_generator.setup_room(current_room_x, current_room_y, current_level, "")

	print("Room generated at (", current_room_x, ",", current_room_y, ") level ", current_level)

func spawn_player():
	# Create player
	player = Player.new()
	player.name = "Player"

	# Create a simple colored rectangle as a temporary sprite
	var color_rect = ColorRect.new()
	color_rect.size = Vector2(16, 16)
	color_rect.color = Color.GREEN
	color_rect.position = Vector2(-8, -8)  # Center the rectangle
	player.add_child(color_rect)

	# Set up collision
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(12, 12)  # Much smaller than sprite for better gameplay
	collision_shape.shape = rect_shape
	player.add_child(collision_shape)

	# Position player at spawn point
	var spawn_pos = room_generator.get_spawn_position()
	player.set_spawn_position(spawn_pos)

	add_child(player)

	# Camera stays fixed - no following

	print("Player spawned at: ", spawn_pos)

func spawn_robots():
	# Get player spawn cell to avoid it
	var player_spawn_cell = room_generator.get_player_spawn_cell()

	# Spawn 3-5 robots in different cells, avoiding player cell
	var num_robots = randi_range(3, 5)
	var robot_colors = [Color.RED, Color.BLUE, Color.YELLOW, Color.MAGENTA]
	var used_cells = [player_spawn_cell]  # Track used cells

	for i in range(num_robots):
		var robot = BerzerkRobot.new()
		robot.robot_color = robot_colors[i % robot_colors.size()]

		# Find an unused cell
		var cell_x = -1
		var cell_y = -1
		var attempts = 0

		while attempts < 20:  # Prevent infinite loop
			cell_x = randi_range(0, 4)  # 5 columns
			cell_y = randi_range(0, 2)  # 3 rows
			var test_cell = Vector2i(cell_x, cell_y)

			if not test_cell in used_cells:
				used_cells.append(test_cell)
				break
			attempts += 1

		# Convert cell to world position
		var world_x = cell_x * 160 + 80  # Center of cell
		var world_y = cell_y * 120 + 60  # Center of cell

		robot.global_position = Vector2(world_x, world_y)
		add_child(robot)

		print("Spawned robot at cell (", cell_x, ",", cell_y, ") world: ", robot.global_position)

func _process(delta):
	# Check if player has exited the screen
	if player:
		check_screen_exit()
