# Main.gd - Main game scene controller
extends Node2D

# Scene references
@onready var room_generator: RoomGenerator
@onready var player: Player
@onready var camera: Camera2D

# We'll create these programmatically since we don't have .tscn files yet

func _ready():
	# Initialize the game
	setup_camera()
	create_room()
	spawn_player()
	setup_hud()

	# Start the game
	GameManager.start_new_game()

func setup_hud():
	# Connect GameManager signals to HUD
	var hud = $CanvasLayer/HUD
	var score_label = hud.get_node("ScoreLabel")
	var lives_label = hud.get_node("LivesLabel")
	var room_label = hud.get_node("RoomLabel")

	# Connect signals
	GameManager.score_changed.connect(_on_score_changed)
	GameManager.lives_changed.connect(_on_lives_changed)
	GameManager.room_changed.connect(_on_room_changed)

func _on_score_changed(new_score: int):
	var score_label = $CanvasLayer/HUD/ScoreLabel
	score_label.text = "SCORE: " + str(new_score)

func _on_lives_changed(new_lives: int):
	var lives_label = $CanvasLayer/HUD/LivesLabel
	lives_label.text = "LIVES: " + str(new_lives)

func _on_room_changed(new_room: int):
	var room_label = $CanvasLayer/HUD/RoomLabel
	room_label.text = "ROOM " + str(new_room)

func setup_camera():
	# Create camera for pixel-perfect rendering
	camera = Camera2D.new()
	camera.enabled = true
	camera.zoom = Vector2.ONE  # 1:1 pixel ratio
	add_child(camera)

func create_room():
	# Create room generator
	room_generator = RoomGenerator.new()
	add_child(room_generator)
	
	print("Room generated with exits at: ", room_generator.get_exit_positions())

func spawn_player():
	# Create player
	player = Player.new()
	player.name = "Player"

	# Create a simple colored rectangle as a temporary sprite
	var color_rect = ColorRect.new()
	color_rect.size = Vector2(16, 16)
	color_rect.color = Color.GREEN
	color_rect.position = Vector2(-8, -8)  # Center the rectangle
	player.add_child(color_rect)

	# Set up collision
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(14, 14)  # Slightly smaller than sprite for better gameplay
	collision_shape.shape = rect_shape
	player.add_child(collision_shape)

	# Position player at spawn point
	var spawn_pos = room_generator.get_spawn_position()
	player.set_spawn_position(spawn_pos)

	add_child(player)

	# Make camera follow player
	camera.global_position = player.global_position

	print("Player spawned at: ", spawn_pos)

func _process(delta):
	# Update camera to follow player
	if player and camera:
		camera.global_position = player.global_position
