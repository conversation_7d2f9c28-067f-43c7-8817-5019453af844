# Main.gd - Main game scene controller
extends Node2D

# Scene references
@onready var room_generator: RoomGenerator
@onready var player: Player
@onready var camera: Camera2D

# Preloaded scenes
var player_scene = preload("res://scenes/Player.tscn")
var room_generator_scene = preload("res://scenes/RoomGenerator.tscn")

func _ready():
	# Initialize the game
	setup_camera()
	create_room()
	spawn_player()
	
	# Start the game
	GameManager.start_new_game()

func setup_camera():
	# Create camera for pixel-perfect rendering
	camera = Camera2D.new()
	camera.enabled = true
	camera.zoom = Vector2.ONE  # 1:1 pixel ratio
	add_child(camera)

func create_room():
	# Create room generator
	room_generator = RoomGenerator.new()
	add_child(room_generator)
	
	print("Room generated with exits at: ", room_generator.get_exit_positions())

func spawn_player():
	# Create player
	player = Player.new()
	
	# Set up player sprite (temporary colored rectangle)
	var sprite = AnimatedSprite2D.new()
	player.add_child(sprite)
	
	# Create a simple green rectangle for the player
	var sprite_frames = SpriteFrames.new()
	sprite_frames.add_animation("idle")
	sprite_frames.add_animation("walk")
	
	# For now, create a simple texture
	var image = Image.create(16, 16, false, Image.FORMAT_RGBA8)
	image.fill(Color.GREEN)
	var texture = ImageTexture.create_from_image(image)
	
	sprite_frames.add_frame("idle", texture)
	sprite_frames.add_frame("walk", texture)
	sprite.sprite_frames = sprite_frames
	
	# Set up collision
	var collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(14, 14)  # Slightly smaller than sprite for better gameplay
	collision_shape.shape = rect_shape
	player.add_child(collision_shape)
	
	# Position player at spawn point
	var spawn_pos = room_generator.get_spawn_position()
	player.set_spawn_position(spawn_pos)
	
	add_child(player)
	
	# Make camera follow player
	camera.global_position = player.global_position
	
	print("Player spawned at: ", spawn_pos)

func _process(delta):
	# Update camera to follow player
	if player and camera:
		camera.global_position = player.global_position
