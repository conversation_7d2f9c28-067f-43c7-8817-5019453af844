# BerzerkRobot.gd - Authentic Berzerk robot AI implementation
extends CharacterBody2D
class_name BerzerkRobot

# Robot properties
@export var move_speed: float = 60.0
@export var robot_color: Color = Color.RED

# Berzerk grid system
const CELL_WIDTH = 160  # pixels (10 tiles * 16px)
const CELL_HEIGHT = 120 # pixels (7.5 tiles * 16px, rounded to 120)
const WALL_AVOID_DISTANCE = 20  # pixels from wall edge

# Robot state
var player_reference: Node2D
var current_cell: Vector2i
var desired_direction: Vector2 = Vector2.ZERO
var can_fire: bool = true
var fire_cooldown: float = 1.0
var fire_timer: float = 0.0

# Visual components
var sprite: ColorRect
var collision_shape: CollisionShape2D

func _ready():
	# Set up robot visuals
	sprite = ColorRect.new()
	sprite.size = Vector2(16, 16)
	sprite.color = robot_color
	sprite.position = Vector2(-8, -8)  # Center the rectangle
	add_child(sprite)
	
	# Set up collision
	collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(14, 14)
	collision_shape.shape = rect_shape
	add_child(collision_shape)
	
	# Set up physics
	collision_layer = 3  # Enemy layer
	collision_mask = 1   # Collide with walls
	add_to_group("robots")
	
	# Find player reference
	player_reference = get_tree().get_first_node_in_group("player")
	
	# Calculate initial cell
	update_current_cell()

func _physics_process(delta):
	fire_timer += delta
	
	if player_reference:
		# Update current cell position
		update_current_cell()
		
		# Berzerk AI: Track player and avoid walls
		calculate_desired_movement()
		apply_wall_avoidance()
		
		# Apply movement
		velocity = desired_direction * move_speed
		move_and_slide()
		
		# Check for firing opportunity
		check_firing_line()

func update_current_cell():
	# Calculate which 3x5 grid cell the robot is in
	current_cell.x = int(global_position.x / CELL_WIDTH)
	current_cell.y = int(global_position.y / CELL_HEIGHT)
	
	# Clamp to valid range
	current_cell.x = clamp(current_cell.x, 0, 4)  # 5 columns (0-4)
	current_cell.y = clamp(current_cell.y, 0, 2)  # 3 rows (0-2)

func calculate_desired_movement():
	# Berzerk Rule: Always try to move toward player
	var direction_to_player = (player_reference.global_position - global_position).normalized()
	
	# Convert to 8-directional movement
	desired_direction = Vector2.ZERO
	
	# Horizontal component
	if abs(direction_to_player.x) > 0.3:
		desired_direction.x = sign(direction_to_player.x)
	
	# Vertical component  
	if abs(direction_to_player.y) > 0.3:
		desired_direction.y = sign(direction_to_player.y)
	
	# Normalize for diagonal movement
	if desired_direction.length() > 0:
		desired_direction = desired_direction.normalized()

func apply_wall_avoidance():
	# Berzerk Rules for wall avoidance:
	# 1. Don't move closer than WALL_AVOID_DISTANCE to walls in current cell
	# 2. If diagonal blocked, move along one component
	# 3. If both components blocked, stop
	
	var original_direction = desired_direction
	var can_move_x = can_move_in_direction(Vector2(desired_direction.x, 0))
	var can_move_y = can_move_in_direction(Vector2(0, desired_direction.y))
	
	# Special case: If player and robot in same cell, ignore walls (Berzerk behavior)
	var player_cell = Vector2i(int(player_reference.global_position.x / CELL_WIDTH), 
							   int(player_reference.global_position.y / CELL_HEIGHT))
	if player_cell == current_cell:
		# Move directly toward player, ignoring walls
		return
	
	# Apply wall avoidance rules
	if not can_move_x and not can_move_y:
		# Both directions blocked - stop
		desired_direction = Vector2.ZERO
	elif not can_move_x:
		# Horizontal blocked - move only vertically
		desired_direction = Vector2(0, desired_direction.y)
	elif not can_move_y:
		# Vertical blocked - move only horizontally  
		desired_direction = Vector2(desired_direction.x, 0)
	# else: both directions clear, keep diagonal movement

func can_move_in_direction(dir: Vector2) -> bool:
	# Check if moving in this direction would hit a wall
	var test_position = global_position + dir * WALL_AVOID_DISTANCE
	
	# Use raycast to check for walls
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsRayQueryParameters2D.create(
		global_position,
		test_position
	)
	query.exclude = [self]
	query.collision_mask = 1  # Only check walls
	
	var result = space_state.intersect_ray(query)
	return result.is_empty()  # True if no wall hit

func check_firing_line():
	# Berzerk Firing Rule: Fire when player's center aligns with robot's 8-directional lines
	if not can_fire or fire_timer < fire_cooldown:
		return
	
	var to_player = player_reference.global_position - global_position
	var distance = to_player.length()
	
	# Check if player is on one of the 8 firing lines
	var firing_direction = Vector2.ZERO
	
	# Check 8 directions
	var directions = [
		Vector2(1, 0), Vector2(-1, 0), Vector2(0, 1), Vector2(0, -1),  # Cardinal
		Vector2(1, 1), Vector2(-1, 1), Vector2(1, -1), Vector2(-1, -1) # Diagonal
	]
	
	for dir in directions:
		var angle_to_dir = to_player.normalized().angle_to(dir)
		if abs(angle_to_dir) < 0.1:  # Small tolerance for alignment
			firing_direction = dir
			break
	
	if firing_direction != Vector2.ZERO:
		fire_laser(firing_direction)

func fire_laser(direction: Vector2):
	# Create laser (load the script and instantiate)
	var laser_script = preload("res://scripts/Laser.gd")
	var laser = laser_script.new()
	get_parent().add_child(laser)
	laser.global_position = global_position
	laser.set_direction(direction)
	laser.set_owner_type("robot")

	# Reset fire timer
	fire_timer = 0.0

	print("Robot fired laser in direction: ", direction)

func take_damage():
	# Robot destroyed
	create_destruction_effect()
	queue_free()

func create_destruction_effect():
	# Simple explosion effect
	var explosion = ColorRect.new()
	explosion.size = Vector2(24, 24)
	explosion.color = Color.ORANGE
	explosion.position = global_position - Vector2(12, 12)
	get_parent().add_child(explosion)
	
	# Fade out explosion
	var tween = create_tween()
	tween.tween_property(explosion, "modulate:a", 0.0, 0.3)
	tween.tween_callback(explosion.queue_free)

# Debug function to visualize current cell
func _draw():
	if Engine.is_editor_hint():
		return
		
	# Draw cell boundaries for debugging
	var cell_top_left = Vector2(current_cell.x * CELL_WIDTH, current_cell.y * CELL_HEIGHT)
	var cell_size = Vector2(CELL_WIDTH, CELL_HEIGHT)
	
	# Convert to local coordinates
	cell_top_left -= global_position
	
	draw_rect(Rect2(cell_top_left, cell_size), Color.YELLOW, false, 2.0)
