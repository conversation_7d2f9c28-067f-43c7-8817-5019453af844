# GameManager.gd - Singleton for managing game state
extends Node

# Game state
enum GameState {
	MENU,
	PLAYING,
	PAUSED,
	GAME_OVER,
	ROOM_TRANSITION
}

var current_state: GameState = GameState.MENU
var score: int = 0
var lives: int = 3
var current_room: int = 1
var rooms_cleared: int = 0

# Game settings
var master_volume: float = 0.8
var sfx_volume: float = 1.0
var music_volume: float = 0.6

# References
var player: Node2D
var current_room_scene: Node2D

# Signals
signal score_changed(new_score: int)
signal lives_changed(new_lives: int)
signal room_changed(new_room: int)
signal game_over
signal room_cleared

func _ready():
	# Set up the singleton
	process_mode = Node.PROCESS_MODE_ALWAYS

func _input(event):
	if event.is_action_pressed("pause"):
		toggle_pause()

func start_new_game():
	score = 0
	lives = 3
	current_room = 1
	rooms_cleared = 0
	current_state = GameState.PLAYING
	
	emit_signal("score_changed", score)
	emit_signal("lives_changed", lives)
	emit_signal("room_changed", current_room)

func add_score(points: int):
	score += points
	emit_signal("score_changed", score)

func lose_life():
	lives -= 1
	emit_signal("lives_changed", lives)
	
	if lives <= 0:
		game_over()
	else:
		# Respawn player
		respawn_player()

func respawn_player():
	if player:
		# Reset player position to room start
		player.respawn()

func next_room():
	current_room += 1
	rooms_cleared += 1
	emit_signal("room_changed", current_room)
	emit_signal("room_cleared")
	
	# Load next room
	load_room(current_room)

func load_room(room_number: int):
	current_state = GameState.ROOM_TRANSITION
	# Room loading logic will be implemented when we create the room system
	current_state = GameState.PLAYING

func toggle_pause():
	if current_state == GameState.PLAYING:
		current_state = GameState.PAUSED
		get_tree().paused = true
	elif current_state == GameState.PAUSED:
		current_state = GameState.PLAYING
		get_tree().paused = false

func game_over():
	current_state = GameState.GAME_OVER
	emit_signal("game_over")

func quit_game():
	get_tree().quit()

# Save/Load system (basic implementation)
func save_game():
	var save_data = {
		"high_score": score,
		"current_room": current_room,
		"rooms_cleared": rooms_cleared,
		"settings": {
			"master_volume": master_volume,
			"sfx_volume": sfx_volume,
			"music_volume": music_volume
		}
	}
	
	var save_file = FileAccess.open("user://robotwars_save.json", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()

func load_game():
	var save_file = FileAccess.open("user://robotwars_save.json", FileAccess.READ)
	if save_file:
		var json_string = save_file.get_as_text()
		save_file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			var save_data = json.data
			if save_data.has("settings"):
				master_volume = save_data.settings.get("master_volume", 0.8)
				sfx_volume = save_data.settings.get("sfx_volume", 1.0)
				music_volume = save_data.settings.get("music_volume", 0.6)
