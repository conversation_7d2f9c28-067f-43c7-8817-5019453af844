# Player.gd - Player character controller for RobotWars
extends CharacterBody2D
class_name Player

# Player properties
@export var move_speed: float = 120.0
@export var acceleration: float = 800.0
@export var friction: float = 800.0

# Player state
var is_alive: bool = true
var last_movement_direction: Vector2 = Vector2.RIGHT
var spawn_position: Vector2

# References (will be set up dynamically)
var sprite: ColorRect
var collision_shape: CollisionShape2D

func _ready():
	# Set up player
	collision_layer = 2  # Player layer
	collision_mask = 1   # Collide with walls
	add_to_group("player")

	# Store spawn position
	spawn_position = global_position

	# Connect to GameManager
	GameManager.player = self

	# Get references to child nodes
	sprite = get_node("ColorRect") if has_node("ColorRect") else null
	collision_shape = get_node("CollisionShape2D") if has_node("CollisionShape2D") else null

func _physics_process(delta):
	if not is_alive:
		return
	
	handle_input()
	apply_movement(delta)
	move_and_slide()
	check_wall_collision()
	update_animation()

func handle_input():
	# Get input direction
	var input_direction = Vector2.ZERO

	if Input.is_action_pressed("move_up"):
		input_direction.y -= 1
	if Input.is_action_pressed("move_down"):
		input_direction.y += 1
	if Input.is_action_pressed("move_left"):
		input_direction.x -= 1
	if Input.is_action_pressed("move_right"):
		input_direction.x += 1

	# Debug: Print input when detected
	if input_direction.length() > 0:
		print("Input detected: ", input_direction)

	# Normalize for 8-directional movement
	if input_direction.length() > 0:
		input_direction = input_direction.normalized()
		last_movement_direction = input_direction

	# Apply movement
	if input_direction.length() > 0:
		velocity = velocity.move_toward(input_direction * move_speed, acceleration * get_physics_process_delta_time())
	else:
		velocity = velocity.move_toward(Vector2.ZERO, friction * get_physics_process_delta_time())

func apply_movement(delta):
	# Movement is handled in handle_input()
	# This function is kept for potential future enhancements
	pass

func check_wall_collision():
	# Check if player is touching a wall (instant death in Berzerk style)
	for i in get_slide_collision_count():
		var collision = get_slide_collision(i)
		var collider = collision.get_collider()
		
		if collider and collider.is_in_group("walls"):
			die("wall")
			return

func update_animation():
	if not sprite:
		return

	# Simple color change based on movement for now
	if velocity.length() > 10:  # Moving
		sprite.color = Color.LIME_GREEN  # Slightly different green when moving
	else:
		sprite.color = Color.GREEN  # Normal green when idle

func fire_laser():
	# This will be implemented when we add the shooting system
	print("Player fired laser in direction: ", last_movement_direction)

func die(cause: String = "unknown"):
	if not is_alive:
		return
	
	is_alive = false
	print("Player died from: ", cause)
	
	# Visual death effect
	create_death_effect()
	
	# Hide player
	visible = false
	
	# Notify GameManager
	GameManager.lose_life()

func create_death_effect():
	# Simple white flash effect
	var flash = ColorRect.new()
	flash.color = Color.WHITE
	flash.size = get_viewport().size
	flash.position = Vector2.ZERO
	get_tree().current_scene.add_child(flash)
	
	# Fade out the flash
	var tween = create_tween()
	tween.tween_property(flash, "modulate:a", 0.0, 0.2)
	tween.tween_callback(flash.queue_free)

func respawn():
	# Reset player state
	is_alive = true
	visible = true
	global_position = spawn_position
	velocity = Vector2.ZERO
	
	print("Player respawned at: ", spawn_position)

func set_spawn_position(pos: Vector2):
	spawn_position = pos
	global_position = pos

# Debug function to visualize player state
func _draw():
	if not is_alive:
		return
	
	# Draw movement direction indicator (for debugging)
	if last_movement_direction.length() > 0:
		draw_line(Vector2.ZERO, last_movement_direction * 20, Color.GREEN, 2)

func _input(event):
	if not is_alive:
		return
	
	# Handle shooting
	if event.is_action_pressed("fire"):
		fire_laser()
