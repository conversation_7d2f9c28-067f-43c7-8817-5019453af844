# Player.gd - Player character controller for RobotWars
extends CharacterBody2D
class_name Player

# Player properties
@export var move_speed: float = 120.0
@export var acceleration: float = 800.0
@export var friction: float = 800.0

# Player state
var is_alive: bool = true
var last_movement_direction: Vector2 = Vector2.RIGHT
var spawn_position: Vector2

# References
@onready var sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D

func _ready():
	# Set up player
	collision_layer = 2  # Player layer
	collision_mask = 1   # Collide with walls
	add_to_group("player")
	
	# Store spawn position
	spawn_position = global_position
	
	# Connect to GameManager
	GameManager.player = self

func _physics_process(delta):
	if not is_alive:
		return
	
	handle_input()
	apply_movement(delta)
	move_and_slide()
	check_wall_collision()
	update_animation()

func handle_input():
	# Get input direction
	var input_direction = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		input_direction.y -= 1
	if Input.is_action_pressed("move_down"):
		input_direction.y += 1
	if Input.is_action_pressed("move_left"):
		input_direction.x -= 1
	if Input.is_action_pressed("move_right"):
		input_direction.x += 1
	
	# Normalize for 8-directional movement
	if input_direction.length() > 0:
		input_direction = input_direction.normalized()
		last_movement_direction = input_direction
	
	# Apply movement
	if input_direction.length() > 0:
		velocity = velocity.move_toward(input_direction * move_speed, acceleration * get_physics_process_delta_time())
	else:
		velocity = velocity.move_toward(Vector2.ZERO, friction * get_physics_process_delta_time())

func apply_movement(delta):
	# Movement is handled in handle_input()
	# This function is kept for potential future enhancements
	pass

func check_wall_collision():
	# Check if player is touching a wall (instant death in Berzerk style)
	for i in get_slide_collision_count():
		var collision = get_slide_collision(i)
		var collider = collision.get_collider()
		
		if collider and collider.is_in_group("walls"):
			die("wall")
			return

func update_animation():
	if not sprite:
		return
	
	# Update sprite direction based on movement
	if velocity.length() > 10:  # Moving
		# Determine primary direction for sprite facing
		if abs(last_movement_direction.x) > abs(last_movement_direction.y):
			# Horizontal movement
			sprite.flip_h = last_movement_direction.x < 0
		
		# Play walk animation
		if sprite.sprite_frames and sprite.sprite_frames.has_animation("walk"):
			sprite.play("walk")
		else:
			# Fallback if no walk animation
			sprite.frame = (sprite.frame + 1) % 4 if Engine.get_process_frames() % 10 == 0 else sprite.frame
	else:
		# Idle
		if sprite.sprite_frames and sprite.sprite_frames.has_animation("idle"):
			sprite.play("idle")
		else:
			sprite.frame = 0

func fire_laser():
	# This will be implemented when we add the shooting system
	print("Player fired laser in direction: ", last_movement_direction)

func die(cause: String = "unknown"):
	if not is_alive:
		return
	
	is_alive = false
	print("Player died from: ", cause)
	
	# Visual death effect
	create_death_effect()
	
	# Hide player
	visible = false
	
	# Notify GameManager
	GameManager.lose_life()

func create_death_effect():
	# Simple white flash effect
	var flash = ColorRect.new()
	flash.color = Color.WHITE
	flash.size = get_viewport().size
	flash.position = Vector2.ZERO
	get_tree().current_scene.add_child(flash)
	
	# Fade out the flash
	var tween = create_tween()
	tween.tween_property(flash, "modulate:a", 0.0, 0.2)
	tween.tween_callback(flash.queue_free)

func respawn():
	# Reset player state
	is_alive = true
	visible = true
	global_position = spawn_position
	velocity = Vector2.ZERO
	
	print("Player respawned at: ", spawn_position)

func set_spawn_position(pos: Vector2):
	spawn_position = pos
	global_position = pos

# Debug function to visualize player state
func _draw():
	if not is_alive:
		return
	
	# Draw movement direction indicator (for debugging)
	if last_movement_direction.length() > 0:
		draw_line(Vector2.ZERO, last_movement_direction * 20, Color.GREEN, 2)

func _input(event):
	if not is_alive:
		return
	
	# Handle shooting
	if event.is_action_pressed("fire"):
		fire_laser()
