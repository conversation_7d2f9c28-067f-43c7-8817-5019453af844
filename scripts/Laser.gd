# Laser.gd - Laser projectile for RobotWars
extends Area2D
class_name Laser

# Laser properties
@export var speed: float = 200.0
@export var lifetime: float = 3.0

# Laser state
var direction: Vector2 = Vector2.RIGHT
var owner_type: String = "player"  # "player" or "robot"

# References
var sprite: ColorRect
var collision_shape: CollisionShape2D

func _ready():
	# Set up laser visuals
	sprite = ColorRect.new()
	sprite.size = Vector2(8, 2)  # Small white rectangle
	sprite.color = Color.WHITE
	sprite.position = Vector2(-4, -1)  # Center the rectangle
	add_child(sprite)
	
	# Set up collision
	collision_shape = CollisionShape2D.new()
	var rect_shape = RectangleShape2D.new()
	rect_shape.size = Vector2(6, 2)
	collision_shape.shape = rect_shape
	add_child(collision_shape)
	
	# Set up physics layers
	collision_layer = 4  # Projectile layer
	collision_mask = 1 | 2 | 3  # Collide with walls, player, enemies
	
	# Connect collision signal
	body_entered.connect(_on_body_entered)
	area_entered.connect(_on_area_entered)
	
	# Set up lifetime timer
	var timer = Timer.new()
	timer.wait_time = lifetime
	timer.one_shot = true
	timer.timeout.connect(_on_lifetime_expired)
	add_child(timer)
	timer.start()
	
	print("Laser created, direction: ", direction, " owner: ", owner_type)

func _physics_process(delta):
	# Move laser
	global_position += direction * speed * delta

func set_direction(new_direction: Vector2):
	direction = new_direction.normalized()
	
	# Rotate sprite to match direction
	if sprite:
		sprite.rotation = direction.angle()

func set_owner_type(new_owner: String):
	owner_type = new_owner

func _on_body_entered(body):
	print("Laser hit body: ", body.name, " (", body.get_class(), ")")
	
	# Check what we hit
	if body.is_in_group("walls"):
		# Hit wall
		hit_wall()
	elif body.is_in_group("player") and owner_type != "player":
		# Robot laser hit player
		hit_player(body)
	elif body.is_in_group("robots") and owner_type != "robot":
		# Player laser hit robot
		hit_robot(body)

func _on_area_entered(area):
	# Handle area collisions (other lasers, etc.)
	if area is Laser and area != self:
		# Laser hit another laser
		destroy_laser()

func hit_wall():
	print("Laser hit wall at: ", global_position)
	# Create small impact effect
	create_impact_effect()
	destroy_laser()

func hit_player(player):
	print("Laser hit player!")
	if player.has_method("die"):
		player.die("laser")
	destroy_laser()

func hit_robot(robot):
	print("Laser hit robot!")
	if robot.has_method("take_damage"):
		robot.take_damage()
	# Award points to player
	if GameManager:
		GameManager.add_score(50)
	destroy_laser()

func create_impact_effect():
	# Simple white flash effect
	var flash = ColorRect.new()
	flash.size = Vector2(8, 8)
	flash.color = Color.WHITE
	flash.position = global_position - Vector2(4, 4)
	get_parent().add_child(flash)
	
	# Fade out the flash
	var tween = create_tween()
	tween.tween_property(flash, "modulate:a", 0.0, 0.1)
	tween.tween_callback(flash.queue_free)

func _on_lifetime_expired():
	print("Laser expired after ", lifetime, " seconds")
	destroy_laser()

func destroy_laser():
	queue_free()
