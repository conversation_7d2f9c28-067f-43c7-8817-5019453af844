[gd_scene load_steps=3 format=3 uid="uid://bqmxvkf8ixs2r"]

[ext_resource type="Script" uid="uid://ly4r3a2cjnu2" path="res://scripts/Main.gd" id="1_0s8vr"]

[sub_resource type="LabelSettings" id="LabelSettings_1"]

[node name="Main" type="Node2D"]
script = ExtResource("1_0s8vr")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="HUD" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="ScoreLabel" type="Label" parent="CanvasLayer/HUD"]
layout_mode = 0
offset_right = 200.0
offset_bottom = 30.0
text = "SCORE: 0"
label_settings = SubResource("LabelSettings_1")

[node name="LivesLabel" type="Label" parent="CanvasLayer/HUD"]
layout_mode = 0
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_bottom = 30.0
text = "LIVES: 3"
label_settings = SubResource("LabelSettings_1")
horizontal_alignment = 2

[node name="RoomLabel" type="Label" parent="CanvasLayer/HUD"]
layout_mode = 0
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_right = 100.0
offset_bottom = 30.0
text = "ROOM 1"
label_settings = SubResource("LabelSettings_1")
horizontal_alignment = 1
